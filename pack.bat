@echo off
echo 建筑艺术学院签到检查系统 - 快速打包
echo =====================================
echo.

echo 清理旧文件...
if exist build rmdir /s /q build
if exist dist rmdir /s /q dist

echo.
echo 开始打包...
pyinstaller --onefile --windowed --name "建筑艺术学院签到检查系统" --add-data "logo.jpg;." --icon "logo.jpg" --exclude-module torch --exclude-module torchvision --exclude-module tensorflow --exclude-module sklearn --exclude-module scipy --exclude-module matplotlib --exclude-module IPython --exclude-module jupyter --exclude-module notebook --exclude-module sphinx --exclude-module babel --exclude-module jinja2 --exclude-module tkinter --exclude-module turtle --exclude-module docutils --exclude-module pygments --exclude-module zmq --exclude-module cryptography --exclude-module distributed --exclude-module dask --exclude-module h5py --exclude-module lxml --exclude-module boto3 --exclude-module transformers --exclude-module bokeh --exclude-module plotly --exclude-module altair --exclude-module skimage --exclude-module astropy --exclude-module imageio --exclude-module win32com --exclude-module pythoncom --exclude-module pywintypes attendance_checker.py

echo.
if exist "dist\建筑艺术学院签到检查系统.exe" (
    echo ✅ 打包成功！
    echo 复制logo文件...
    copy logo.jpg dist\ >nul 2>&1
    
    echo 创建安装脚本...
    echo @echo off > "dist\安装程序.bat"
    echo echo ======================================== >> "dist\安装程序.bat"
    echo echo 建筑艺术学院签到检查系统 - 安装程序 >> "dist\安装程序.bat"
    echo echo ======================================== >> "dist\安装程序.bat"
    echo echo. >> "dist\安装程序.bat"
    echo echo 正在复制程序文件... >> "dist\安装程序.bat"
    echo if not exist "建筑艺术学院签到检查系统" mkdir "建筑艺术学院签到检查系统" >> "dist\安装程序.bat"
    echo copy "建筑艺术学院签到检查系统.exe" "建筑艺术学院签到检查系统\\" >> "dist\安装程序.bat"
    echo copy "logo.jpg" "建筑艺术学院签到检查系统\\" 2^>nul >> "dist\安装程序.bat"
    echo echo. >> "dist\安装程序.bat"
    echo echo ✅ 安装完成！ >> "dist\安装程序.bat"
    echo echo 程序已安装到：%%CD%%\\建筑艺术学院签到检查系统 >> "dist\安装程序.bat"
    echo pause >> "dist\安装程序.bat"
    
    echo.
    echo 🎉 打包完成！
    echo 📁 输出目录：dist\
    echo 🚀 可执行文件：dist\建筑艺术学院签到检查系统.exe
    echo 📦 安装脚本：dist\安装程序.bat
) else (
    echo ❌ 打包失败
)

echo.
pause
