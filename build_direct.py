#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接打包脚本 - 使用最简单的方式
"""

import os
import shutil

def build_direct():
    """直接使用pyinstaller命令打包"""
    print("🔨 开始直接打包...")
    
    # 清理之前的构建文件
    if os.path.exists('build'):
        shutil.rmtree('build')
        print("🧹 已清理build目录")
    
    if os.path.exists('dist'):
        shutil.rmtree('dist')
        print("🧹 已清理dist目录")
    
    # 构建排除列表
    excludes = [
        'torch', 'torchvision', 'tensorflow', 'sklearn', 'scipy', 'matplotlib',
        'IPython', 'jupyter', 'notebook', 'sphinx', 'babel', 'jinja2',
        'tkinter', 'turtle', 'docutils', 'pygments', 'zmq', 'cryptography',
        'distributed', 'dask', 'h5py', 'lxml', 'boto3', 'transformers',
        'bokeh', 'plotly', 'altair', 'skimage', 'astropy', 'imageio',
        'win32com', 'pythoncom', 'pywintypes'
    ]
    
    exclude_str = ' '.join([f'--exclude-module {mod}' for mod in excludes])
    
    # 使用最简单的命令
    cmd = f'''pyinstaller --onefile --windowed --name "建筑艺术学院签到检查系统" --add-data "logo.jpg;." --icon "logo.jpg" {exclude_str} attendance_checker.py'''
    
    print(f"执行命令: {cmd}")
    result = os.system(cmd)
    
    if result == 0:
        print("✅ 打包完成！")
        print("📁 可执行文件位置：dist/建筑艺术学院签到检查系统.exe")
        
        # 复制logo到dist目录
        if os.path.exists('logo.jpg'):
            shutil.copy2('logo.jpg', 'dist/')
            print("✅ 已复制logo.jpg到dist目录")
            
        create_installer_script()
        
    else:
        print("❌ 打包失败")

def create_installer_script():
    """创建安装脚本"""
    installer_content = '''@echo off
echo ========================================
echo 建筑艺术学院签到检查系统 - 安装程序
echo ========================================
echo.

echo 正在复制程序文件...
if not exist "建筑艺术学院签到检查系统" mkdir "建筑艺术学院签到检查系统"

copy "建筑艺术学院签到检查系统.exe" "建筑艺术学院签到检查系统\\"
copy "logo.jpg" "建筑艺术学院签到检查系统\\" 2>nul

echo.
echo 正在创建桌面快捷方式...
echo Set oWS = WScript.CreateObject("WScript.Shell") > CreateShortcut.vbs
echo sLinkFile = "%USERPROFILE%\\Desktop\\建筑艺术学院签到检查系统.lnk" >> CreateShortcut.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> CreateShortcut.vbs
echo oLink.TargetPath = "%CD%\\建筑艺术学院签到检查系统\\建筑艺术学院签到检查系统.exe" >> CreateShortcut.vbs
echo oLink.WorkingDirectory = "%CD%\\建筑艺术学院签到检查系统" >> CreateShortcut.vbs
echo oLink.Description = "建筑艺术学院签到检查系统" >> CreateShortcut.vbs
echo oLink.Save >> CreateShortcut.vbs

cscript CreateShortcut.vbs >nul 2>&1
del CreateShortcut.vbs >nul 2>&1

echo.
echo ✅ 安装完成！
echo.
echo 程序已安装到：%CD%\\建筑艺术学院签到检查系统
echo 桌面快捷方式已创建
echo.
echo 现在可以通过桌面快捷方式或直接运行程序
echo.
pause
'''
    
    with open('dist/安装程序.bat', 'w', encoding='gbk') as f:
        f.write(installer_content)
    
    print("✅ 已创建安装脚本：dist/安装程序.bat")

def main():
    """主函数"""
    print("=" * 50)
    print("建筑艺术学院签到检查系统 - 直接打包工具")
    print("=" * 50)
    
    # 检查必要文件
    if not os.path.exists('attendance_checker.py'):
        print("❌ 错误：找不到 attendance_checker.py 文件")
        return
    
    if not os.path.exists('logo.jpg'):
        print("⚠️  警告：找不到 logo.jpg 文件，将使用默认图标")
    
    build_direct()
    
    print("\n" + "=" * 50)
    print("🎉 打包完成！")
    print("📁 输出目录：dist/")
    print("🚀 可执行文件：dist/建筑艺术学院签到检查系统.exe")
    print("📦 安装脚本：dist/安装程序.bat")
    print("=" * 50)

if __name__ == "__main__":
    main()
