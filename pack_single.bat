@echo off
chcp 65001 >nul
echo 建筑艺术学院签到比对系统 - 单文件打包
echo =========================================
echo.

echo 清理旧文件...
if exist build rmdir /s /q build
if exist dist rmdir /s /q dist

echo.
echo 开始打包单个exe文件...
echo 这可能需要几分钟时间，请耐心等待...
echo.

pyinstaller --onefile --windowed --name "建筑艺术学院签到比对系统" --add-data "logo.jpg;." --icon "logo.jpg" --hidden-import "pandas._libs.tslibs.timedeltas" --hidden-import "pandas._libs.tslibs.np_datetime" --hidden-import "pandas._libs.tslibs.nattype" --hidden-import "pandas._libs.properties" --hidden-import "openpyxl.cell._writer" --exclude-module torch --exclude-module torchvision --exclude-module tensorflow --exclude-module sklearn --exclude-module scipy --exclude-module matplotlib --exclude-module IPython --exclude-module jupyter --exclude-module sphinx --exclude-module babel --exclude-module jinja2 --exclude-module tkinter --exclude-module docutils --exclude-module pygments --exclude-module zmq --exclude-module cryptography --exclude-module distributed --exclude-module dask --exclude-module h5py --exclude-module lxml --exclude-module boto3 --exclude-module transformers --exclude-module bokeh --exclude-module plotly --exclude-module skimage --exclude-module astropy --exclude-module imageio --exclude-module win32com --exclude-module pythoncom --exclude-module pywintypes --exclude-module numba --exclude-module llvmlite attendance_checker.py

echo.
if exist "dist\建筑艺术学院签到比对系统.exe" (
    echo ✅ 打包成功！
    echo.
    echo 文件信息：
    dir "dist\建筑艺术学院签到比对系统.exe"
    echo.
    echo 🎉 单文件exe已创建完成！
    echo 📁 文件位置：dist\建筑艺术学院签到比对系统.exe
    echo 📦 这是一个独立的可执行文件，包含了所有必要的环境
    echo 💡 可以直接复制到任何Windows电脑上运行
) else (
    echo ❌ 打包失败，请检查错误信息
)

echo.
pause
