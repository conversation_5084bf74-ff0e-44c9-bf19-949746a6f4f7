#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版PyInstaller打包脚本
只打包必要的依赖，避免包含不必要的大型库
"""

import os
import sys
import shutil

def create_simple_spec():
    """创建简化的spec文件，排除不必要的包"""
    spec_content = '''
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 排除不必要的大型包
excludes = [
    'torch', 'torchvision', 'tensorflow', 'sklearn', 'scipy', 'matplotlib',
    'numpy.random._pickle', 'numpy.random._bounded_integers',
    'IPython', 'jupyter', 'notebook', 'sphinx', 'babel', 'jinja2',
    'PIL.ImageQt', 'PIL.ImageTk', 'tkinter', 'turtle',
    'docutils', 'pygments', 'zmq', 'cryptography', 'bcrypt',
    'distributed', 'dask', 'h5py', 'lxml', 'boto3', 'botocore',
    'transformers', 'regex', 'sacremoses', 'onnxruntime',
    'bokeh', 'plotly', 'altair', 'panel', 'param', 'holoviews',
    'skimage', 'astropy', 'imageio', 'tifffile', 'paramiko',
    'keyring', 'ruamel', 'tables', 'sqlalchemy', 'psycopg2',
    'MySQLdb', 'win32com', 'pythoncom', 'pywintypes'
]

a = Analysis(
    ['attendance_checker.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('logo.jpg', '.'),  # 包含logo文件
    ],
    hiddenimports=[
        'pandas._libs.tslibs.timedeltas',
        'pandas._libs.tslibs.np_datetime',
        'pandas._libs.tslibs.nattype',
        'pandas._libs.properties',
        'openpyxl.cell._writer',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# 手动移除不需要的模块
a.binaries = [x for x in a.binaries if not any(exclude in x[0].lower() for exclude in [
    'torch', 'sklearn', 'scipy', 'matplotlib', 'tensorflow', 'mkl', 'intel',
    'cuda', 'cudnn', 'cublas', 'cufft', 'curand', 'cusparse', 'cusolver'
])]

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='建筑艺术学院签到检查系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='logo.jpg',  # 使用logo作为图标
)
'''
    
    with open('attendance_simple.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 已创建简化版 attendance_simple.spec 文件")

def build_simple():
    """执行简化打包"""
    print("🔨 开始简化打包...")
    
    # 清理之前的构建文件
    if os.path.exists('build'):
        shutil.rmtree('build')
        print("🧹 已清理build目录")
    
    if os.path.exists('dist'):
        shutil.rmtree('dist')
        print("🧹 已清理dist目录")
    
    # 使用更精确的参数执行打包
    cmd = 'pyinstaller attendance_simple.spec --clean --noconfirm'
    print(f"执行命令: {cmd}")
    result = os.system(cmd)
    
    if result == 0:
        print("✅ 打包完成！")
        print("📁 可执行文件位置：dist/建筑艺术学院签到检查系统.exe")
    else:
        print("❌ 打包失败")

def create_minimal_env_script():
    """创建最小环境安装脚本"""
    script_content = '''@echo off
echo 创建最小打包环境...
echo.

echo 创建新的conda环境...
conda create -n attendance_minimal python=3.11 -y

echo 激活环境...
call conda activate attendance_minimal

echo 安装必要依赖...
pip install pandas==1.5.3
pip install PyQt5==5.15.7
pip install openpyxl==3.0.10
pip install xlrd==2.0.2
pip install pyinstaller==6.16.0

echo.
echo ✅ 最小环境创建完成！
echo 请在新环境中运行打包命令：
echo conda activate attendance_minimal
echo python build_simple.py
echo.
pause
'''
    
    with open('create_minimal_env.bat', 'w', encoding='gbk') as f:
        f.write(script_content)
    
    print("✅ 已创建最小环境脚本：create_minimal_env.bat")

def main():
    """主函数"""
    print("=" * 50)
    print("建筑艺术学院签到检查系统 - 简化打包工具")
    print("=" * 50)
    
    # 检查必要文件
    if not os.path.exists('attendance_checker.py'):
        print("❌ 错误：找不到 attendance_checker.py 文件")
        return
    
    if not os.path.exists('logo.jpg'):
        print("⚠️  警告：找不到 logo.jpg 文件，将使用默认图标")
    
    print("\n选择操作：")
    print("1. 在当前环境中简化打包（推荐）")
    print("2. 创建最小环境脚本")
    
    choice = input("\n请输入选择 (1 或 2): ").strip()
    
    if choice == "1":
        # 创建简化spec文件
        create_simple_spec()
        
        # 执行简化打包
        build_simple()
        
        # 创建安装脚本
        if os.path.exists('dist'):
            create_installer_script()
            
            # 复制logo到dist目录
            if os.path.exists('logo.jpg'):
                shutil.copy2('logo.jpg', 'dist/')
                print("✅ 已复制logo.jpg到dist目录")
        
        print("\n" + "=" * 50)
        print("🎉 简化打包完成！")
        print("📁 输出目录：dist/")
        print("🚀 可执行文件：dist/建筑艺术学院签到检查系统.exe")
        print("=" * 50)
        
    elif choice == "2":
        create_minimal_env_script()
        print("\n请运行 create_minimal_env.bat 创建最小环境")
        
    else:
        print("无效选择")

def create_installer_script():
    """创建安装脚本"""
    installer_content = '''@echo off
echo ========================================
echo 建筑艺术学院签到检查系统 - 安装程序
echo ========================================
echo.

echo 正在复制程序文件...
if not exist "建筑艺术学院签到检查系统" mkdir "建筑艺术学院签到检查系统"

copy "建筑艺术学院签到检查系统.exe" "建筑艺术学院签到检查系统\\"
copy "logo.jpg" "建筑艺术学院签到检查系统\\" 2>nul

echo.
echo 正在创建桌面快捷方式...
echo Set oWS = WScript.CreateObject("WScript.Shell") > CreateShortcut.vbs
echo sLinkFile = "%USERPROFILE%\\Desktop\\建筑艺术学院签到检查系统.lnk" >> CreateShortcut.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> CreateShortcut.vbs
echo oLink.TargetPath = "%CD%\\建筑艺术学院签到检查系统\\建筑艺术学院签到检查系统.exe" >> CreateShortcut.vbs
echo oLink.WorkingDirectory = "%CD%\\建筑艺术学院签到检查系统" >> CreateShortcut.vbs
echo oLink.Description = "建筑艺术学院签到检查系统" >> CreateShortcut.vbs
echo oLink.Save >> CreateShortcut.vbs

cscript CreateShortcut.vbs >nul 2>&1
del CreateShortcut.vbs >nul 2>&1

echo.
echo ✅ 安装完成！
echo.
echo 程序已安装到：%CD%\\建筑艺术学院签到检查系统
echo 桌面快捷方式已创建
echo.
echo 现在可以通过桌面快捷方式或直接运行程序
echo.
pause
'''
    
    with open('dist/安装程序.bat', 'w', encoding='gbk') as f:
        f.write(installer_content)
    
    print("✅ 已创建安装脚本：dist/安装程序.bat")

if __name__ == "__main__":
    main()
