('E:\\TLabel\\FindAbsence\\build\\attendance_simple\\建筑艺术学院签到检查系统.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'E:\\TLabel\\FindAbsence\\build\\attendance_simple\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'E:\\TLabel\\FindAbsence\\build\\attendance_simple\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'E:\\TLabel\\FindAbsence\\build\\attendance_simple\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'E:\\TLabel\\FindAbsence\\build\\attendance_simple\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'E:\\TLabel\\FindAbsence\\build\\attendance_simple\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'E:\\TLabel\\FindAbsence\\build\\attendance_simple\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'D:\\Anaconda\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\Anaconda\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\Anaconda\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\Anaconda\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'D:\\Anaconda\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'D:\\Anaconda\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_traitlets',
   'D:\\Anaconda\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_traitlets.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   'D:\\Anaconda\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('attendance_checker',
   'E:\\TLabel\\FindAbsence\\attendance_checker.py',
   'PYSOURCE'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'D:\\Anaconda\\Library\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'D:\\Anaconda\\Library\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('msvcp140_2.dll', 'D:\\Anaconda\\Library\\bin\\msvcp140_2.dll', 'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'D:\\Anaconda\\Library\\bin\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'D:\\Anaconda\\Library\\bin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'D:\\Anaconda\\Library\\bin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'D:\\Anaconda\\Library\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('omptarget.sycl.wrap.dll',
   'D:\\Anaconda\\Library\\bin\\omptarget.sycl.wrap.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'D:\\Anaconda\\Library\\bin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('concrt140.dll', 'D:\\Anaconda\\Library\\bin\\concrt140.dll', 'BINARY'),
  ('msvcp140_1.dll', 'D:\\Anaconda\\Library\\bin\\msvcp140_1.dll', 'BINARY'),
  ('vcruntime140_1.dll',
   'D:\\Anaconda\\Library\\bin\\vcruntime140_1.dll',
   'BINARY'),
  ('tbbmalloc.dll', 'D:\\Anaconda\\Library\\bin\\tbbmalloc.dll', 'BINARY'),
  ('vccorlib140.dll', 'D:\\Anaconda\\Library\\bin\\vccorlib140.dll', 'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'D:\\Anaconda\\Library\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'D:\\Anaconda\\Library\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'D:\\Anaconda\\Library\\bin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('ucrtbase.dll', 'D:\\Anaconda\\Library\\bin\\ucrtbase.dll', 'BINARY'),
  ('omptarget.rtl.opencl.dll',
   'D:\\Anaconda\\Library\\bin\\omptarget.rtl.opencl.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'D:\\Anaconda\\Library\\bin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'D:\\Anaconda\\Library\\bin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('tcl86t.dll', 'D:\\Anaconda\\Library\\bin\\tcl86t.dll', 'BINARY'),
  ('omptarget.dll', 'D:\\Anaconda\\Library\\bin\\omptarget.dll', 'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'D:\\Anaconda\\Library\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('ffi-7.dll', 'D:\\Anaconda\\Library\\bin\\ffi-7.dll', 'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'D:\\Anaconda\\Library\\bin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'D:\\Anaconda\\Library\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'D:\\Anaconda\\Library\\bin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'D:\\Anaconda\\Library\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'D:\\Anaconda\\Library\\bin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('msvcp140.dll', 'D:\\Anaconda\\Library\\bin\\msvcp140.dll', 'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'D:\\Anaconda\\Library\\bin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('libbz2.dll', 'D:\\Anaconda\\Library\\bin\\libbz2.dll', 'BINARY'),
  ('vcomp140.dll', 'D:\\Anaconda\\Library\\bin\\vcomp140.dll', 'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'D:\\Anaconda\\Library\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('omptarget.rtl.level0.dll',
   'D:\\Anaconda\\Library\\bin\\omptarget.rtl.level0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'D:\\Anaconda\\Library\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('tbb12.dll', 'D:\\Anaconda\\Library\\bin\\tbb12.dll', 'BINARY'),
  ('libiomp5md.dll', 'D:\\Anaconda\\Library\\bin\\libiomp5md.dll', 'BINARY'),
  ('libcrypto-1_1-x64.dll',
   'D:\\Anaconda\\Library\\bin\\libcrypto-1_1-x64.dll',
   'BINARY'),
  ('ffi.dll', 'D:\\Anaconda\\Library\\bin\\ffi.dll', 'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'D:\\Anaconda\\Library\\bin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'D:\\Anaconda\\Library\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('sqlite3.dll', 'D:\\Anaconda\\Library\\bin\\sqlite3.dll', 'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'D:\\Anaconda\\Library\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('tk86t.dll', 'D:\\Anaconda\\Library\\bin\\tk86t.dll', 'BINARY'),
  ('zlib.dll', 'D:\\Anaconda\\Library\\bin\\zlib.dll', 'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'D:\\Anaconda\\Library\\bin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'D:\\Anaconda\\Library\\bin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('libiompstubs5md.dll',
   'D:\\Anaconda\\Library\\bin\\libiompstubs5md.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'D:\\Anaconda\\Library\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'D:\\Anaconda\\Library\\bin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-multibyte-l1-1-0.dll',
   'D:\\Anaconda\\Library\\bin\\api-ms-win-crt-multibyte-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'D:\\Anaconda\\Library\\bin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('bzip2.dll', 'D:\\Anaconda\\Library\\bin\\bzip2.dll', 'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'D:\\Anaconda\\Library\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'D:\\Anaconda\\Library\\bin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'D:\\Anaconda\\Library\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'D:\\Anaconda\\Library\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'D:\\Anaconda\\Library\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'D:\\Anaconda\\Library\\bin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'D:\\Anaconda\\Library\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('tbbmalloc_proxy.dll',
   'D:\\Anaconda\\Library\\bin\\tbbmalloc_proxy.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'D:\\Anaconda\\Library\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'D:\\Anaconda\\Library\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('ffi-8.dll', 'D:\\Anaconda\\Library\\bin\\ffi-8.dll', 'BINARY'),
  ('liblzma.dll', 'D:\\Anaconda\\Library\\bin\\liblzma.dll', 'BINARY'),
  ('libssl-1_1-x64.dll',
   'D:\\Anaconda\\Library\\bin\\libssl-1_1-x64.dll',
   'BINARY'),
  ('libimalloc.dll', 'D:\\Anaconda\\Library\\bin\\libimalloc.dll', 'BINARY'),
  ('vcruntime140.dll',
   'D:\\Anaconda\\Library\\bin\\vcruntime140.dll',
   'BINARY'),
  ('msvcp140_codecvt_ids.dll',
   'D:\\Anaconda\\Library\\bin\\msvcp140_codecvt_ids.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'D:\\Anaconda\\Library\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('pyarrow\\arrow_python.dll',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\arrow_python.dll',
   'BINARY'),
  ('pyarrow\\arrow_python_flight.dll',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\arrow_python_flight.dll',
   'BINARY'),
  ('python311.dll', 'D:\\Anaconda\\python311.dll', 'BINARY'),
  ('libGLESv2.dll', 'D:\\Anaconda\\Library\\bin\\libGLESv2.dll', 'BINARY'),
  ('opengl32sw.dll', 'D:\\Anaconda\\Library\\bin\\opengl32sw.dll', 'BINARY'),
  ('libEGL.dll', 'D:\\Anaconda\\Library\\bin\\libEGL.dll', 'BINARY'),
  ('icuin58.dll', 'D:\\Anaconda\\Library\\bin\\icuin58.dll', 'BINARY'),
  ('icuuc58.dll', 'D:\\Anaconda\\Library\\bin\\icuuc58.dll', 'BINARY'),
  ('icudt58.dll', 'D:\\Anaconda\\Library\\bin\\icudt58.dll', 'BINARY'),
  ('llvmlite\\binding\\llvmlite.dll',
   'D:\\Anaconda\\Lib\\site-packages\\llvmlite\\binding\\llvmlite.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'D:\\Anaconda\\Library\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'D:\\Anaconda\\Library\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'D:\\Anaconda\\Library\\plugins\\platforms\\qwebgl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'D:\\Anaconda\\Library\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'D:\\Anaconda\\Library\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qpdf.dll',
   'D:\\Anaconda\\Library\\plugins\\imageformats\\qpdf.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'D:\\Anaconda\\Library\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'D:\\Anaconda\\Library\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'D:\\Anaconda\\Library\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'D:\\Anaconda\\Library\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'D:\\Anaconda\\Library\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'D:\\Anaconda\\Library\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforminputcontexts\\qtvirtualkeyboardplugin.dll',
   'D:\\Anaconda\\Library\\plugins\\platforminputcontexts\\qtvirtualkeyboardplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'D:\\Anaconda\\Library\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'D:\\Anaconda\\Library\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qdirect2d.dll',
   'D:\\Anaconda\\Library\\plugins\\platforms\\qdirect2d.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'D:\\Anaconda\\Library\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'D:\\Anaconda\\Library\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'D:\\Anaconda\\Library\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'D:\\Anaconda\\Library\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('archive.dll', 'D:\\Anaconda\\Library\\bin\\archive.dll', 'BINARY'),
  ('_decimal.pyd', 'D:\\Anaconda\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'D:\\Anaconda\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'D:\\Anaconda\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\Anaconda\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('unicodedata.pyd', 'D:\\Anaconda\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('select.pyd', 'D:\\Anaconda\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'D:\\Anaconda\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'D:\\Anaconda\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_queue.pyd', 'D:\\Anaconda\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'D:\\Anaconda\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('pyexpat.pyd', 'D:\\Anaconda\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('win32\\win32api.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('win32\\win32evtlog.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\win32\\win32evtlog.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\Anaconda\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('_overlapped.pyd', 'D:\\Anaconda\\DLLs\\_overlapped.pyd', 'EXTENSION'),
  ('_asyncio.pyd', 'D:\\Anaconda\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('_elementtree.pyd', 'D:\\Anaconda\\DLLs\\_elementtree.pyd', 'EXTENSION'),
  ('openpyxl\\utils\\cell.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\utils\\cell.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\_webp.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_tests.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\core\\_multiarray_tests.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\psutil\\_psutil_windows.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_umath.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\core\\_multiarray_umath.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32pdh.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\lapack_lite.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\linalg\\lapack_lite.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\random\\mtrand.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\random\\_sfc64.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\random\\_philox.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\random\\_pcg64.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\random\\_mt19937.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\random\\bit_generator.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\random\\_generator.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\random\\_common.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_internal.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\fft\\_pocketfft_internal.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\_cffi_backend.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\_imaging.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('openpyxl\\worksheet\\_reader.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\worksheet\\_reader.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('openpyxl\\worksheet\\_writer.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\worksheet\\_writer.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\properties.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_libs\\properties.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\vectorized.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_libs\\tslibs\\vectorized.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timestamps.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timestamps.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\period.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_libs\\tslibs\\period.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\offsets.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_libs\\tslibs\\offsets.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashtable.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_libs\\hashtable.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_compute.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\_compute.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32console.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\win32\\win32console.pyd',
   'EXTENSION'),
  ('win32\\win32event.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\win32\\win32event.pyd',
   'EXTENSION'),
  ('win32\\win32gui.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\win32\\win32gui.pyd',
   'EXTENSION'),
  ('win32\\win32file.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\win32\\win32file.pyd',
   'EXTENSION'),
  ('win32\\win32security.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\win32\\win32security.pyd',
   'EXTENSION'),
  ('win32\\win32pipe.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\win32\\win32pipe.pyd',
   'EXTENSION'),
  ('win32\\win32process.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\win32\\win32process.pyd',
   'EXTENSION'),
  ('_uuid.pyd', 'D:\\Anaconda\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('zope\\interface\\_zope_interface_coptimizations.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\zope\\interface\\_zope_interface_coptimizations.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_s3fs.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\_s3fs.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_parquet_encryption.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\_parquet_encryption.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_parquet.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\_parquet.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_orc.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\_orc.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_json.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\_json.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_hdfs.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\_hdfs.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_fs.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\_fs.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_flight.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\_flight.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_exec_plan.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\_exec_plan.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_dataset_parquet.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\_dataset_parquet.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_dataset_orc.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\_dataset_orc.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_dataset.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\_dataset.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_csv.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\_csv.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('tornado\\speedups.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\tornado\\speedups.pyd',
   'EXTENSION'),
  ('pyarrow\\_hdfsio.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\_hdfsio.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('brotli\\_brotli.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\brotli\\_brotli.pyd',
   'EXTENSION'),
  ('ujson.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\ujson.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('yarl\\_quoting_c.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\yarl\\_quoting_c.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('multidict\\_multidict.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\multidict\\_multidict.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('aiohttp\\_http_writer.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\aiohttp\\_http_writer.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('aiohttp\\_http_parser.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\aiohttp\\_http_parser.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('frozenlist\\_frozenlist.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\frozenlist\\_frozenlist.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('aiohttp\\_helpers.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\aiohttp\\_helpers.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('aiohttp\\_websocket.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\aiohttp\\_websocket.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('zstandard\\_cffi.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\zstandard\\_cffi.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('zstandard\\backend_c.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\zstandard\\backend_c.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('lz4\\frame\\_frame.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\lz4\\frame\\_frame.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('lz4\\_version.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\lz4\\_version.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('snappy\\_snappy.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\snappy\\_snappy.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\sip.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\PyQt5\\sip.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd', 'D:\\Anaconda\\DLLs\\_sqlite3.pyd', 'EXTENSION'),
  ('pandas\\_libs\\sparse.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_libs\\sparse.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_libs\\ops.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\groupby.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_libs\\groupby.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('yaml\\_yaml.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\yaml\\_yaml.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numba\\core\\typeconv\\_typeconv.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\typeconv\\_typeconv.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numba\\np\\ufunc\\workqueue.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\np\\ufunc\\workqueue.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numba\\np\\ufunc\\omppool.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\np\\ufunc\\omppool.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numba\\np\\ufunc\\tbbpool.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\np\\ufunc\\tbbpool.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numba\\np\\ufunc\\_internal.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\np\\ufunc\\_internal.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numba\\core\\runtime\\_nrt_python.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\runtime\\_nrt_python.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numba\\experimental\\jitclass\\_box.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\experimental\\jitclass\\_box.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numba\\_devicearray.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\_devicearray.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numba\\mviewbuf.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\mviewbuf.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numba\\_dispatcher.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\_dispatcher.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numba\\_helperlib.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\_helperlib.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numba\\_dynfunc.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\_dynfunc.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numexpr\\interpreter.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\numexpr\\interpreter.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\join.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_libs\\join.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\testing.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_libs\\testing.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashing.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_libs\\hashing.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\aggregations.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_libs\\window\\aggregations.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_feather.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\_feather.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\lib.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\lib.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\ccalendar.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_libs\\tslibs\\ccalendar.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\arrays.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_libs\\arrays.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\fields.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_libs\\tslibs\\fields.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\strptime.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_libs\\tslibs\\strptime.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\parsing.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_libs\\tslibs\\parsing.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\parsers.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_libs\\parsers.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\json.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_libs\\json.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\indexers.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_libs\\window\\indexers.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('markupsafe\\_speedups.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\markupsafe\\_speedups.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops_dispatch.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_libs\\ops_dispatch.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\indexing.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_libs\\indexing.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\tzconversion.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_libs\\tslibs\\tzconversion.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timezones.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timezones.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\conversion.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_libs\\tslibs\\conversion.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\dtypes.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_libs\\tslibs\\dtypes.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslib.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_libs\\tslib.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\writers.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_libs\\writers.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\internals.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_libs\\internals.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\reshape.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_libs\\reshape.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\reduction.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_libs\\reduction.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\index.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_libs\\index.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\missing.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_libs\\missing.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\algos.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_libs\\algos.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\lib.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_libs\\lib.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\interval.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_libs\\interval.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\nattype.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_libs\\tslibs\\nattype.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\np_datetime.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_libs\\tslibs\\np_datetime.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timedeltas.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timedeltas.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\QtGui.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\PyQt5\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt5\\QtCore.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\PyQt5\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWidgets.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\PyQt5\\QtWidgets.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\base.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_libs\\tslibs\\base.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\io\\sas\\_sas.cp311-win_amd64.pyd',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\sas\\_sas.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('arrow.dll', 'D:\\Anaconda\\Library\\bin\\arrow.dll', 'BINARY'),
  ('parquet.dll', 'D:\\Anaconda\\Library\\bin\\parquet.dll', 'BINARY'),
  ('arrow_flight.dll',
   'D:\\Anaconda\\Library\\bin\\arrow_flight.dll',
   'BINARY'),
  ('Qt5Gui_conda.dll',
   'D:\\Anaconda\\Library\\bin\\Qt5Gui_conda.dll',
   'BINARY'),
  ('Qt5Svg_conda.dll',
   'D:\\Anaconda\\Library\\bin\\Qt5Svg_conda.dll',
   'BINARY'),
  ('Qt5Core_conda.dll',
   'D:\\Anaconda\\Library\\bin\\Qt5Core_conda.dll',
   'BINARY'),
  ('Qt5WebSockets_conda.dll',
   'D:\\Anaconda\\Library\\bin\\Qt5WebSockets_conda.dll',
   'BINARY'),
  ('Qt5Quick_conda.dll',
   'D:\\Anaconda\\Library\\bin\\Qt5Quick_conda.dll',
   'BINARY'),
  ('Qt5Network_conda.dll',
   'D:\\Anaconda\\Library\\bin\\Qt5Network_conda.dll',
   'BINARY'),
  ('libpng16.dll', 'D:\\Anaconda\\Library\\bin\\libpng16.dll', 'BINARY'),
  ('Qt5Pdf_conda.dll',
   'D:\\Anaconda\\Library\\bin\\Qt5Pdf_conda.dll',
   'BINARY'),
  ('libjpeg.dll', 'D:\\Anaconda\\Library\\bin\\libjpeg.dll', 'BINARY'),
  ('Qt5DBus_conda.dll',
   'D:\\Anaconda\\Library\\bin\\Qt5DBus_conda.dll',
   'BINARY'),
  ('Qt5VirtualKeyboard_conda.dll',
   'D:\\Anaconda\\Library\\bin\\Qt5VirtualKeyboard_conda.dll',
   'BINARY'),
  ('Qt5Qml_conda.dll',
   'D:\\Anaconda\\Library\\bin\\Qt5Qml_conda.dll',
   'BINARY'),
  ('Qt5Widgets_conda.dll',
   'D:\\Anaconda\\Library\\bin\\Qt5Widgets_conda.dll',
   'BINARY'),
  ('iconv.dll', 'D:\\Anaconda\\Library\\bin\\iconv.dll', 'BINARY'),
  ('zstd.dll', 'D:\\Anaconda\\Library\\bin\\zstd.dll', 'BINARY'),
  ('libxml2.dll', 'D:\\Anaconda\\Library\\bin\\libxml2.dll', 'BINARY'),
  ('charset.dll', 'D:\\Anaconda\\Library\\bin\\charset.dll', 'BINARY'),
  ('liblz4.dll', 'D:\\Anaconda\\Library\\bin\\liblz4.dll', 'BINARY'),
  ('pywin32_system32\\pywintypes311.dll',
   'D:\\Anaconda\\Lib\\site-packages\\win32\\pywintypes311.dll',
   'BINARY'),
  ('tiff.dll', 'D:\\Anaconda\\Library\\bin\\tiff.dll', 'BINARY'),
  ('arrow_dataset.dll',
   'D:\\Anaconda\\Library\\bin\\arrow_dataset.dll',
   'BINARY'),
  ('python3.dll', 'D:\\Anaconda\\python3.dll', 'BINARY'),
  ('snappy.dll', 'D:\\Anaconda\\Library\\bin\\snappy.dll', 'BINARY'),
  ('yaml.dll', 'D:\\Anaconda\\Library\\bin\\yaml.dll', 'BINARY'),
  ('aws-cpp-sdk-identity-management.dll',
   'D:\\Anaconda\\Library\\bin\\aws-cpp-sdk-identity-management.dll',
   'BINARY'),
  ('aws-cpp-sdk-core.dll',
   'D:\\Anaconda\\Library\\bin\\aws-cpp-sdk-core.dll',
   'BINARY'),
  ('aws-cpp-sdk-s3.dll',
   'D:\\Anaconda\\Library\\bin\\aws-cpp-sdk-s3.dll',
   'BINARY'),
  ('orc.dll', 'D:\\Anaconda\\Library\\bin\\orc.dll', 'BINARY'),
  ('brotlienc.dll', 'D:\\Anaconda\\Library\\bin\\brotlienc.dll', 'BINARY'),
  ('re2.dll', 'D:\\Anaconda\\Library\\bin\\re2.dll', 'BINARY'),
  ('utf8proc.dll', 'D:\\Anaconda\\Library\\bin\\utf8proc.dll', 'BINARY'),
  ('brotlidec.dll', 'D:\\Anaconda\\Library\\bin\\brotlidec.dll', 'BINARY'),
  ('thriftmd.dll', 'D:\\Anaconda\\Library\\bin\\thriftmd.dll', 'BINARY'),
  ('libprotobuf.dll', 'D:\\Anaconda\\Library\\bin\\libprotobuf.dll', 'BINARY'),
  ('cares.dll', 'D:\\Anaconda\\Library\\bin\\cares.dll', 'BINARY'),
  ('Qt5QmlModels_conda.dll',
   'D:\\Anaconda\\Library\\bin\\Qt5QmlModels_conda.dll',
   'BINARY'),
  ('Lerc.dll', 'D:\\Anaconda\\Library\\bin\\Lerc.dll', 'BINARY'),
  ('deflate.dll', 'D:\\Anaconda\\Library\\bin\\deflate.dll', 'BINARY'),
  ('aws-cpp-sdk-sts.dll',
   'D:\\Anaconda\\Library\\bin\\aws-cpp-sdk-sts.dll',
   'BINARY'),
  ('aws-cpp-sdk-cognito-identity.dll',
   'D:\\Anaconda\\Library\\bin\\aws-cpp-sdk-cognito-identity.dll',
   'BINARY'),
  ('libcurl.dll', 'D:\\Anaconda\\Library\\bin\\libcurl.dll', 'BINARY'),
  ('aws-c-common.dll',
   'D:\\Anaconda\\Library\\bin\\aws-c-common.dll',
   'BINARY'),
  ('aws-c-event-stream.dll',
   'D:\\Anaconda\\Library\\bin\\aws-c-event-stream.dll',
   'BINARY'),
  ('brotlicommon.dll',
   'D:\\Anaconda\\Library\\bin\\brotlicommon.dll',
   'BINARY'),
  ('libssh2.dll', 'D:\\Anaconda\\Library\\bin\\libssh2.dll', 'BINARY'),
  ('aws-checksums.dll',
   'D:\\Anaconda\\Library\\bin\\aws-checksums.dll',
   'BINARY'),
  ('logo.jpg', 'E:\\TLabel\\FindAbsence\\logo.jpg', 'DATA'),
  ('importlib_metadata-6.0.0.dist-info\\INSTALLER',
   'D:\\Anaconda\\Lib\\site-packages\\importlib_metadata-6.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('importlib_metadata-6.0.0.dist-info\\REQUESTED',
   'D:\\Anaconda\\Lib\\site-packages\\importlib_metadata-6.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('importlib_metadata-6.0.0.dist-info\\direct_url.json',
   'D:\\Anaconda\\Lib\\site-packages\\importlib_metadata-6.0.0.dist-info\\direct_url.json',
   'DATA'),
  ('importlib_metadata-6.0.0.dist-info\\METADATA',
   'D:\\Anaconda\\Lib\\site-packages\\importlib_metadata-6.0.0.dist-info\\METADATA',
   'DATA'),
  ('importlib_metadata-6.0.0.dist-info\\WHEEL',
   'D:\\Anaconda\\Lib\\site-packages\\importlib_metadata-6.0.0.dist-info\\WHEEL',
   'DATA'),
  ('importlib_metadata-6.0.0.dist-info\\LICENSE',
   'D:\\Anaconda\\Lib\\site-packages\\importlib_metadata-6.0.0.dist-info\\LICENSE',
   'DATA'),
  ('importlib_metadata-6.0.0.dist-info\\RECORD',
   'D:\\Anaconda\\Lib\\site-packages\\importlib_metadata-6.0.0.dist-info\\RECORD',
   'DATA'),
  ('importlib_metadata-6.0.0.dist-info\\top_level.txt',
   'D:\\Anaconda\\Lib\\site-packages\\importlib_metadata-6.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'D:\\Anaconda\\Lib\\site-packages\\dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\validate.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\validate.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\builder_binary.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\builder_binary.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\portable-snippets\\safe-math.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\portable-snippets\\safe-math.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\buffer_builder.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\buffer_builder.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\string_builder.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\string_builder.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\filesystem.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\filesystem.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\csv.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\csv.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\hash_util.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\hash_util.h',
   'DATA'),
  ('pyarrow\\_cuda.pxd',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\_cuda.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\visitor_generate.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\visitor_generate.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\platform.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\platform.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\align_util.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\align_util.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\datetime.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\datetime.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bitmap_reader.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bitmap_reader.h',
   'DATA'),
  ('pyarrow\\_csv.pyx',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\_csv.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\future.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\future.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\visit_data_inline.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\visit_data_inline.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\api.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\io\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bpacking_simd256_generated.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bpacking_simd256_generated.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\invalid_row.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\csv\\invalid_row.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\async_util.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\async_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\memory.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\memory.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\status.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\status.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\exec\\map_node.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\exec\\map_node.h',
   'DATA'),
  ('pyarrow\\error.pxi',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\error.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\executor_util.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\testing\\executor_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bpacking_avx512.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bpacking_avx512.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\api_scalar.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\api_scalar.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\json_simple.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\json_simple.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\map.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\map.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\adapters\\orc\\adapter.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\adapters\\orc\\adapter.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\init.cc',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\init.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\visit_type_inline.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\visit_type_inline.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\chunk_resolver.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\chunk_resolver.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\datetime\\visibility.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\datetime\\visibility.h',
   'DATA'),
  ('pyarrow\\_hdfsio.pyx',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\_hdfsio.pyx',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\numpy_to_arrow.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\numpy_to_arrow.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\counting_semaphore.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\counting_semaphore.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\udf.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\udf.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\visibility.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\visibility.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\double_conversion.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\double_conversion.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\python_test.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\python_test.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\filesystem.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\filesystem.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\plan.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\plan.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\file_csv.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\file_csv.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\deserialize.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\deserialize.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\xxhash.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\xxhash.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\algorithm.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\algorithm.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\matchers.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\testing\\matchers.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\buffer.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\buffer.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\chunker.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\json\\chunker.h',
   'DATA'),
  ('pyarrow\\_compute.pyx',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\_compute.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\writer.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\csv\\writer.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\decimal.cc',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\decimal.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\numpy_convert.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\numpy_convert.h',
   'DATA'),
  ('pyarrow\\compat.pxi',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\compat.pxi',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\udf.cc',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\udf.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\builder.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\builder.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\extension_type.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\extension_type.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\bignum.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\bignum.h',
   'DATA'),
  ('pyarrow\\includes\\libarrow_flight.pxd',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\includes\\libarrow_flight.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\functional.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\functional.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\device.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\device.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\tensor.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\tensor.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\builder_union.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\builder_union.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\exec\\util.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\exec\\util.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\udf.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\udf.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\builder.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\testing\\builder.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\decimal.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\decimal.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\exec\\hash_join_dict.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\exec\\hash_join_dict.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bit_block_counter.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bit_block_counter.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\pyarrow.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\pyarrow.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\benchmark.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\benchmark.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\memory.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\io\\memory.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\platform.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\platform.h',
   'DATA'),
  ('pyarrow\\array.pxi',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\array.pxi',
   'DATA'),
  ('pyarrow\\includes\\libarrow.pxd',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\includes\\libarrow.pxd',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\common.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\common.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\tensor\\converter.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\tensor\\converter.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compare.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\compare.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\datetime.cc',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\datetime.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\array.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\array.h',
   'DATA'),
  ('pyarrow\\_dataset_orc.pyx',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\_dataset_orc.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bitmap_ops.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bitmap_ops.h',
   'DATA'),
  ('pyarrow\\_flight.pyx',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\_flight.pyx',
   'DATA'),
  ('pyarrow\\includes\\libgandiva.pxd',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\includes\\libgandiva.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\discovery.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\discovery.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\c\\bridge.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\c\\bridge.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\transport.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\transport.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\data.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\data.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\gdb.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\gdb.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\server_middleware.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\server_middleware.h',
   'DATA'),
  ('pyarrow\\_exec_plan.pyx',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\_exec_plan.pyx',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\iterators.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\iterators.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\exec\\aggregate.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\exec\\aggregate.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\datetime\\ios.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\datetime\\ios.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\async_generator.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\async_generator.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\pch.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\pch.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\record_batch.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\record_batch.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bpacking_default.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bpacking_default.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\dictionary.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\dictionary.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bpacking_simd128_generated.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bpacking_simd128_generated.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\platform.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\platform.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\json_integration.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\testing\\json_integration.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\exec\\hash_join_node.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\exec\\hash_join_node.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\async_test_util.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\testing\\async_test_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\memory_pool.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\memory_pool.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\double-conversion.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\double-conversion.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\ipc.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\ipc.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\memory_pool_test.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\memory_pool_test.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\ProducerConsumerQueue.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\ProducerConsumerQueue.h',
   'DATA'),
  ('pyarrow\\_pyarrow_cpp_tests.pxd',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\_pyarrow_cpp_tests.pxd',
   'DATA'),
  ('pyarrow\\pandas-shim.pxi',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\pandas-shim.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\diy-fp.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\diy-fp.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\numpy_convert.cc',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\numpy_convert.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\helpers.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\helpers.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\key_value_metadata.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\key_value_metadata.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\csv.cc',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\csv.cc',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\pyarrow_api.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\pyarrow_api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\kernel.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\kernel.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\pyarrow.cc',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\pyarrow.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\debug.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\debug.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\byte_stream_split.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\byte_stream_split.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\c\\helpers.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\c\\helpers.h',
   'DATA'),
  ('pyarrow\\includes\\libarrow_dataset.pxd',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\includes\\libarrow_dataset.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\datetime\\tz_private.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\datetime\\tz_private.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\api.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\utils.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\utils.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\logging.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\logging.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\io.cc',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\io.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\ipc.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\ipc.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\exec.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\exec.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\ubsan.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\ubsan.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\benchmark_util.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\benchmark_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\middleware.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\middleware.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\unreachable.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\unreachable.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\ieee.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\ieee.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\diff.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\diff.h',
   'DATA'),
  ('pyarrow\\_parquet_encryption.pyx',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\_parquet_encryption.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\queue.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\queue.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\localfs.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\localfs.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\arrow_to_pandas.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\arrow_to_pandas.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\python_to_arrow.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\python_to_arrow.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\datetime\\tz.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\datetime\\tz.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\compression.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\compression.h',
   'DATA'),
  ('pyarrow\\includes\\libarrow_dataset_parquet.pxd',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\includes\\libarrow_dataset_parquet.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\test_util.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\test_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\gdb.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\gdb.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\pch.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\pch.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\visitor.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\visitor.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\pcg_random.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\pcg_random.h',
   'DATA'),
  ('pyarrow\\_dataset_parquet.pyx',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\_dataset_parquet.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\small_vector.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\small_vector.h',
   'DATA'),
  ('pyarrow\\_plasma.pyx',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\_plasma.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\dispatch.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\dispatch.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\windows_fixup.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\windows_fixup.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\rapidjson_defs.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\json\\rapidjson_defs.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\pcg\\pcg_extras.hpp',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\pcg\\pcg_extras.hpp',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\pyarrow_api.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\pyarrow_api.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\deserialize.cc',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\deserialize.cc',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\api.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\api.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\inference.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\inference.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\portable-snippets\\debug-trap.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\portable-snippets\\debug-trap.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\thread_pool.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\thread_pool.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\caching.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\io\\caching.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\c\\abi.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\c\\abi.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\uniform_real.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\testing\\uniform_real.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\arrow_to_pandas.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\arrow_to_pandas.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\partition.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\partition.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\type_fwd.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\type_fwd.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\exec\\asof_join_node.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\exec\\asof_join_node.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\api.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\api.h',
   'DATA'),
  ('pyarrow\\public-api.pxi',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\public-api.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\test_definitions.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\test_definitions.h',
   'DATA'),
  ('pyarrow\\tensorflow\\plasma_op.cc',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\tensorflow\\plasma_op.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\flight.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\flight.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\fast-dtoa.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\fast-dtoa.h',
   'DATA'),
  ('pyarrow\\table.pxi',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\table.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\builder_nested.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\builder_nested.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\datetime.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\datetime.h',
   'DATA'),
  ('pyarrow\\_parquet.pxd',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\_parquet.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\regex.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\regex.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\api.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\api.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\gdb.cc',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\gdb.cc',
   'DATA'),
  ('pyarrow\\_compute.pxd',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\_compute.pxd',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\helpers.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\helpers.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\fixed-dtoa.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\fixed-dtoa.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\dataset.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\dataset.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\sort.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\sort.h',
   'DATA'),
  ('pyarrow\\_feather.pyx',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\_feather.pyx',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\arrow_to_pandas.cc',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\arrow_to_pandas.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\type_fwd.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\type_fwd.h',
   'DATA'),
  ('pyarrow\\includes\\libarrow_feather.pxd',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\includes\\libarrow_feather.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\exec\\partition_util.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\exec\\partition_util.h',
   'DATA'),
  ('pyarrow\\serialization.pxi',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\serialization.pxi',
   'DATA'),
  ('pyarrow\\includes\\libarrow_python.pxd',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\includes\\libarrow_python.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\transport_server.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\transport_server.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\future_util.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\testing\\future_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\byte_size.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\byte_size.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\deserialize.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\deserialize.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\api.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\builder_base.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\builder_base.h',
   'DATA'),
  ('pyarrow\\_orc.pxd',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\_orc.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bitset_stack.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bitset_stack.h',
   'DATA'),
  ('pyarrow\\_gcsfs.pyx',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\_gcsfs.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\gtest_util.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\testing\\gtest_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\int_util.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\int_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\buffered.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\io\\buffered.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\math_constants.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\math_constants.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\exec\\hash_join.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\exec\\hash_join.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\options.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\csv\\options.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bit_stream_utils.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bit_stream_utils.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\delimiting.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\delimiting.h',
   'DATA'),
  ('pyarrow\\lib_api.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\lib_api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\visibility.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\visibility.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\chunked_array.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\chunked_array.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\lib.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\lib.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\inference.cc',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\inference.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bitmap_visit.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bitmap_visit.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\type_traits.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\type_traits.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\feather.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\feather.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\interfaces.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\io\\interfaces.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bit_run_reader.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bit_run_reader.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\decimal.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\decimal.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\builder_primitive.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\builder_primitive.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\base64.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\base64.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\exec\\query_context.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\exec\\query_context.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\array_nested.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\array_nested.h',
   'DATA'),
  ('pyarrow\\_orc.pyx',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\_orc.pyx',
   'DATA'),
  ('pyarrow\\ipc.pxi',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\ipc.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\python_to_arrow.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\python_to_arrow.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\type_fwd.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\type_fwd.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\bignum-dtoa.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\bignum-dtoa.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\random.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\testing\\random.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\array_primitive.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\array_primitive.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\pyarrow_lib.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\pyarrow_lib.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\flight.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\flight.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\test_common.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\test_common.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\test_common.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\io\\test_common.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\hdfs.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\io\\hdfs.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\tdigest.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\tdigest.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\value_parsing.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\value_parsing.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\generator.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\testing\\generator.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\init.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\init.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\visibility.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\visibility.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\writer.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\writer.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\cpu_info.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\cpu_info.h',
   'DATA'),
  ('pyarrow\\lib.pxd',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\lib.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\visit_scalar_inline.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\visit_scalar_inline.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\io.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\io.h',
   'DATA'),
  ('pyarrow\\includes\\__init__.pxd',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\includes\\__init__.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bitmap_generate.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bitmap_generate.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\numpy_interop.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\numpy_interop.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\result.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\result.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\array_dict.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\array_dict.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\decimal.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\decimal.h',
   'DATA'),
  ('pyarrow\\includes\\common.pxd',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\includes\\common.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\s3fs.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\s3fs.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\visit_array_inline.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\visit_array_inline.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\object_writer.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\json\\object_writer.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\scalar.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\scalar.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\exec\\bloom_filter.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\exec\\bloom_filter.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\exec\\accumulation_queue.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\exec\\accumulation_queue.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\type_fwd.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\csv\\type_fwd.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\test_util.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\test_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\client_cookie_middleware.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\client_cookie_middleware.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\file.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\io\\file.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\builder_time.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\builder_time.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\compressed.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\io\\compressed.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\task_group.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\task_group.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\utf8.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\utf8.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\numpy_convert.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\numpy_convert.h',
   'DATA'),
  ('pyarrow\\_fs.pyx',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\_fs.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bitmap.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bitmap.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\type_fwd.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\json\\type_fwd.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\light_array.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\light_array.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\visibility.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\visibility.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\exec\\key_hash.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\exec\\key_hash.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\exec\\benchmark_util.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\exec\\benchmark_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\visibility.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\visibility.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\chunker.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\csv\\chunker.h',
   'DATA'),
  ('pyarrow\\includes\\libarrow_cuda.pxd',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\includes\\libarrow_cuda.pxd',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\numpy_internal.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\numpy_internal.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\extension_type.cc',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\extension_type.cc',
   'DATA'),
  ('pyarrow\\_fs.pxd',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\_fs.pxd',
   'DATA'),
  ('pyarrow\\gandiva.pyx',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\gandiva.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\launder.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\launder.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\aligned_storage.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\aligned_storage.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\sparse_tensor.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\sparse_tensor.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\concatenate.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\concatenate.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\common.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\common.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\type_fwd.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\type_fwd.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\numpy_interop.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\numpy_interop.h',
   'DATA'),
  ('pyarrow\\_s3fs.pyx',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\_s3fs.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\converter.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\csv\\converter.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\stopwatch.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\stopwatch.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\config.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\config.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\cast.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\cast.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\io_util.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\io_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\slow.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\io\\slow.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\projector.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\projector.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\int_util_overflow.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\int_util_overflow.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\reader.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\csv\\reader.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\exec\\order_by_impl.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\exec\\order_by_impl.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\strptime.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\strptime.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bitmap_builders.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bitmap_builders.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\numpy_to_arrow.cc',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\numpy_to_arrow.cc',
   'DATA'),
  ('pyarrow\\scalar.pxi',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\scalar.pxi',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\common.cc',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\common.cc',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\type_traits.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\type_traits.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\parallel.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\parallel.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\table.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\table.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\config.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\config.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\basic_decimal.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\basic_decimal.h',
   'DATA'),
  ('pyarrow\\types.pxi',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\types.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\stl_iterator.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\stl_iterator.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\type_fwd.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\io\\type_fwd.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\init.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\init.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\pch.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\pch.h',
   'DATA'),
  ('pyarrow\\includes\\libarrow_fs.pxd',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\includes\\libarrow_fs.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\array_binary.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\array_binary.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\api.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\concurrency.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\io\\concurrency.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\client.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\client.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\stl_allocator.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\stl_allocator.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\type_traits.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\type_traits.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\builder_adaptive.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\builder_adaptive.h',
   'DATA'),
  ('pyarrow\\_cuda.pyx',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\_cuda.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bpacking.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bpacking.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\file_base.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\file_base.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bit_util.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bit_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\formatting.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\formatting.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\ipc.cc',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\ipc.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\type.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\type.h',
   'DATA'),
  ('pyarrow\\_hdfs.pyx',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\_hdfs.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\api_aggregate.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\api_aggregate.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\extension_type.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\extension_type.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\python_test.cc',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\python_test.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\type_fwd.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\type_fwd.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\object_parser.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\json\\object_parser.h',
   'DATA'),
  ('pyarrow\\arrow_python_flight.lib',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\arrow_python_flight.lib',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\type_traits.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\type_traits.h',
   'DATA'),
  ('pyarrow\\_dataset.pxd',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\_dataset.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\visibility.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\testing\\visibility.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\column_decoder.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\csv\\column_decoder.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\print.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\print.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\gtest_compat.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\testing\\gtest_compat.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\file_orc.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\file_orc.h',
   'DATA'),
  ('pyarrow\\config.pxi',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\config.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\range.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\range.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\util.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\exec\\test_util.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\exec\\test_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\mutex.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\mutex.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\options.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\options.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\lib_api.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\lib_api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\mockfs.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\mockfs.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\pretty_print.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\pretty_print.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\uri.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\uri.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\row\\grouper.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\row\\grouper.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\spaced.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\spaced.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\parser.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\csv\\parser.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\server.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\server.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\registry.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\registry.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\compare.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\compare.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\converter.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\converter.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bpacking_neon.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bpacking_neon.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\gcsfs.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\gcsfs.h',
   'DATA'),
  ('pyarrow\\_json.pyx',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\_json.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\time.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\time.h',
   'DATA'),
  ('pyarrow\\_parquet.pyx',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\_parquet.pyx',
   'DATA'),
  ('pyarrow\\benchmark.pxi',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\benchmark.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\stl.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\stl.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\options.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\json\\options.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\hdfs.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\hdfs.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\vector.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\vector.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\serialize.cc',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\serialize.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\reader.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\json\\reader.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\filesystem.cc',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\filesystem.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\iterators.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\iterators.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\io.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\io.h',
   'DATA'),
  ('pyarrow\\memory.pxi',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\memory.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\api_vector.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\api_vector.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\server_auth.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\server_auth.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\parquet_encryption.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\parquet_encryption.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\scanner.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\scanner.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\util.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\testing\\util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\datetime\\date.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\datetime\\date.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bpacking64_default.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bpacking64_default.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\path_util.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\path_util.h',
   'DATA'),
  ('pyarrow\\lib.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\lib.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\pch.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\pch.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\mman.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\io\\mman.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\parser.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\json\\parser.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\column_builder.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\csv\\column_builder.h',
   'DATA'),
  ('pyarrow\\__init__.pxd',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\__init__.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\string.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\string.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\pcg\\pcg_uint128.hpp',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\pcg\\pcg_uint128.hpp',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\python_test.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\python_test.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\builder_decimal.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\builder_decimal.h',
   'DATA'),
  ('pyarrow\\_csv.pxd',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\_csv.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\exec\\expression.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\exec\\expression.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\exec\\key_map.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\exec\\key_map.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\pch.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\testing\\pch.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\api.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\csv\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\simd.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\simd.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\adapters\\orc\\options.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\adapters\\orc\\options.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\benchmark.cc',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\benchmark.cc',
   'DATA'),
  ('pyarrow\\includes\\libarrow_substrait.pxd',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\includes\\libarrow_substrait.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\s3_test_util.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\s3_test_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\test_common.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\csv\\test_common.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\cancel.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\cancel.h',
   'DATA'),
  ('pyarrow\\_dataset.pyx',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\_dataset.pyx',
   'DATA'),
  ('pyarrow\\includes\\libplasma.pxd',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\includes\\libplasma.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\tracing.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\tracing.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\array_decimal.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\array_decimal.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\flight.cc',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\flight.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\file_parquet.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\file_parquet.h',
   'DATA'),
  ('pyarrow\\io.pxi',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\io.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\async_generator_fwd.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\async_generator_fwd.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\serialize.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\serialize.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\hashing.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\hashing.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bytes_view.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bytes_view.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\chunked_builder.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\json\\chunked_builder.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\builder_dict.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\builder_dict.h',
   'DATA'),
  ('pyarrow\\_parquet_encryption.pxd',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\_parquet_encryption.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\inference.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\inference.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\converter.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\json\\converter.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\parquet_encryption.cc',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\parquet_encryption.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\file_ipc.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\file_ipc.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\serialize.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\serialize.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\strtod.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\strtod.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\type_fwd.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\type_fwd.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\numpy_to_arrow.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\numpy_to_arrow.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\trie.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\trie.h',
   'DATA'),
  ('pyarrow\\_pyarrow_cpp_tests.pyx',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\_pyarrow_cpp_tests.pyx',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\benchmark.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\benchmark.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\exec\\tpch_node.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\exec\\tpch_node.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\windows_compatibility.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\windows_compatibility.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\pcg\\pcg_random.hpp',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\pcg\\pcg_random.hpp',
   'DATA'),
  ('pyarrow\\arrow_python.lib',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\arrow_python.lib',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\client_middleware.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\client_middleware.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\exec\\exec_plan.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\exec\\exec_plan.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\CMakeLists.txt',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\CMakeLists.txt',
   'DATA'),
  ('pyarrow\\_substrait.pyx',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\_substrait.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bitmap_writer.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bitmap_writer.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\util.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\endian.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\endian.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\csv.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\csv.h',
   'DATA'),
  ('pyarrow\\lib.pyx',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\lib.pyx',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\parquet_encryption.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\parquet_encryption.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\message.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\message.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\macros.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\macros.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\exec\\schema_util.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\exec\\schema_util.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\arrow_to_python_internal.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\arrow_to_python_internal.h',
   'DATA'),
  ('pyarrow\\tensor.pxi',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\tensor.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\concurrent_map.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\concurrent_map.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\type_fwd.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\type_fwd.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\exec\\task_util.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\exec\\task_util.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\helpers.cc',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\helpers.cc',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\pch.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\pch.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\api.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\json\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\types.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\types.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bpacking_avx2.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bpacking_avx2.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\iterator.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\iterator.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\pyarrow.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\pyarrow.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\test_common.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\test_common.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bpacking_simd512_generated.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bpacking_simd512_generated.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\server_tracing_middleware.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\server_tracing_middleware.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\client_auth.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\client_auth.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\function.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\function.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\pyarrow_lib.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\pyarrow_lib.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\reader.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\reader.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\api.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\rle_encoding.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\rle_encoding.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\api.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\table_builder.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\table_builder.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\transform.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\io\\transform.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\array_base.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\array_base.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\filesystem.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\filesystem.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\checked_cast.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\checked_cast.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\xxhash\\xxhash.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\xxhash\\xxhash.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\cached-powers.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\cached-powers.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\stdio.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\io\\stdio.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\datetime.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\datetime.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\datum.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\datum.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\test_util.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\test_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\exec\\options.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\exec\\options.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\exec\\swiss_join.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\exec\\swiss_join.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\extension_type.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\extension_type.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\client_tracing_middleware.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\client_tracing_middleware.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\test_common.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\json\\test_common.h',
   'DATA'),
  ('pyarrow\\builder.pxi',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\builder.pxi',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\python_to_arrow.cc',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\python_to_arrow.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\extension_type.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\testing\\extension_type.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\dataset_writer.h',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\dataset_writer.h',
   'DATA'),
  ('certifi\\py.typed',
   'D:\\Anaconda\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('certifi\\cacert.pem',
   'D:\\Anaconda\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('lz4-4.3.2.dist-info\\WHEEL',
   'D:\\Anaconda\\Lib\\site-packages\\lz4-4.3.2.dist-info\\WHEEL',
   'DATA'),
  ('lz4-4.3.2.dist-info\\REQUESTED',
   'D:\\Anaconda\\Lib\\site-packages\\lz4-4.3.2.dist-info\\REQUESTED',
   'DATA'),
  ('lz4-4.3.2.dist-info\\RECORD',
   'D:\\Anaconda\\Lib\\site-packages\\lz4-4.3.2.dist-info\\RECORD',
   'DATA'),
  ('lz4-4.3.2.dist-info\\METADATA',
   'D:\\Anaconda\\Lib\\site-packages\\lz4-4.3.2.dist-info\\METADATA',
   'DATA'),
  ('lz4-4.3.2.dist-info\\top_level.txt',
   'D:\\Anaconda\\Lib\\site-packages\\lz4-4.3.2.dist-info\\top_level.txt',
   'DATA'),
  ('lz4-4.3.2.dist-info\\direct_url.json',
   'D:\\Anaconda\\Lib\\site-packages\\lz4-4.3.2.dist-info\\direct_url.json',
   'DATA'),
  ('lz4-4.3.2.dist-info\\LICENSE',
   'D:\\Anaconda\\Lib\\site-packages\\lz4-4.3.2.dist-info\\LICENSE',
   'DATA'),
  ('lz4-4.3.2.dist-info\\INSTALLER',
   'D:\\Anaconda\\Lib\\site-packages\\lz4-4.3.2.dist-info\\INSTALLER',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex.tpl',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_table.tpl',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_style.tpl',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html_style.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html.tpl',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\string.tpl',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\formats\\templates\\string.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_table.tpl',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html_table.tpl',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Panama',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Panama',
   'DATA'),
  ('pytz\\zoneinfo\\America\\El_Salvador',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guyana',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guam',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Whitehorse',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+0',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Phoenix',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('pytz\\zoneinfo\\Universal',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Zulu',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Djibouti',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santa_Isabel',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Yancowinna',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Bougainville',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Central',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia_Banderas',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Riyadh',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Johns',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+1',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtau',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Luanda',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Central',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Mountain',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tarawa',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Melbourne',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Paramaribo',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Catamarca',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Calcutta',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fortaleza',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Acre',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Saigon',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bogota',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('pytz\\zoneinfo\\GMT-0',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santiago',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+8',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('pytz\\zoneinfo\\iso3166.tab',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jamaica',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-11',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Nelson',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Niamey',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Gibraltar',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Noronha',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Katmandu',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Istanbul',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rio_Branco',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sofia',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port_of_Spain',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-13',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tallinn',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Juba',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nome',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nome',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Taipei',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yakutat',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashkhabad',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Istanbul',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fiji',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Cocos',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nouakchott',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('pytz\\zoneinfo\\NZ-CHAT',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\NZ-CHAT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Busingen',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Glace_Bay',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Monaco',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tongatapu',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tirane',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lima',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Lima',
   'DATA'),
  ('pytz\\zoneinfo\\UCT',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Marigot',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qostanay',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Antigua',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kanton',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kanton',
   'DATA'),
  ('pytz\\zoneinfo\\EST5EDT',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\EST5EDT',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaSur',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Samara',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('pytz\\zoneinfo\\Egypt',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Egypt',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kiritimati',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Caracas',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\West',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yellowknife',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Canary',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Sao_Tome',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Amman',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Barnaul',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Casablanca',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Hawaii',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mazatlan',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Azores',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Resolute',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guadeloupe',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hebron',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atka',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Atka',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Eastern',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Oral',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Atlantic',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Manila',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Martinique',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Knox_IN',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tehran',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Creston',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Creston',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Efate',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Skopje',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Douala',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('pytz\\zoneinfo\\CET',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\CET',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Dublin',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Makassar',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pontianak',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('pytz\\zoneinfo\\America\\La_Paz',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guayaquil',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('pytz\\zoneinfo\\HST',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\HST',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yangon',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Inuvik',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ceuta',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT0',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Davis',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kabul',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Greenwich',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Amsterdam',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Algiers',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Canberra',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Denver',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Denver',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Funafuti',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Ponape',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Accra',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Michigan',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Sydney',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('pytz\\zoneinfo\\WET',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\WET',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Antananarivo',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Midway',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Miquelon',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Stockholm',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Minsk',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rosario',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Luxembourg',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Aleutian',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pitcairn',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Urumqi',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\South',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\South',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Punta_Arenas',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Queensland',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\Acre',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-5',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Scoresbysund',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Vostok',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\London',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\London',
   'DATA'),
  ('pytz\\zoneinfo\\ROC',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\ROC',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bangkok',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Salta',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nassau',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Galapagos',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Winnipeg',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\ACT',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kwajalein',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Abidjan',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Thomas',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\McMurdo',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+6',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmera',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sao_Paulo',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macao',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('pytz\\zoneinfo\\MST',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\MST',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tijuana',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thule',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Thule',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+11',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Samarkand',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dakar',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lord_Howe',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('pytz\\zoneinfo\\Turkey',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Turkey',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Apia',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Casey',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tripoli',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chungking',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Moncton',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+10',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Victoria',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jujuy',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Stanley',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Managua',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Managua',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Windhoek',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Helsinki',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tbilisi',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chongqing',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Jersey',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vientiane',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('pytz\\zoneinfo\\US\\East-Indiana',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Matamoros',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coral_Harbour',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zaporozhye',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mexico_City',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UCT',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Harbin',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ndjamena',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('pytz\\zoneinfo\\PRC',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\PRC',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Shiprock',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ojinaga',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yakutsk',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Monrovia',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montevideo',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('pytz\\zoneinfo\\GB',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\GB',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Mountain',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Shanghai',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Buenos_Aires',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Indiana-Starke',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hong_Kong',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Nicosia',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Juneau',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('pytz\\zoneinfo\\Iceland',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Iceland',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Reunion',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('pytz\\zoneinfo\\Factory',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Factory',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bissau',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Colombo',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Riga',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Niue',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('pytz\\zoneinfo\\America\\New_York',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\New_York',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Darwin',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Karachi',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaNorte',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Hobart',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Beirut',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vienna',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Harare',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kashgar',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Universal',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\NZ',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\NZ',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ljubljana',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('pytz\\zoneinfo\\MST7MDT',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\MST7MDT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Podgorica',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('pytz\\zoneinfo\\Zulu',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Guernsey',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Cairo',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Tasmania',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belize',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Belize',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cambridge_Bay',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thunder_Bay',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Perth',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Johannesburg',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baku',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cancun',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Banjul',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wallis',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lusaka',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boa_Vista',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guatemala',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zagreb',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belgrade',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Adelaide',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maseru',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Godthab',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Toronto',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Damascus',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pohnpei',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Lisbon',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('pytz\\zoneinfo\\Poland',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Poland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anchorage',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('pytz\\zoneinfo\\Greenwich',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Kwajalein',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Choibalsan',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rainy_River',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dushanbe',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bamako',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Warsaw',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Madeira',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\Singapore',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Khartoum',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Pacific',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vaduz',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hovd',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\San_Marino',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('pytz\\zoneinfo\\zone.tab',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\zone.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zurich',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Omsk',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nuuk',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sarajevo',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tortola',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('pytz\\zoneinfo\\CST6CDT',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\CST6CDT',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-7',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Chagos',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('pytz\\zoneinfo\\Japan',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Japan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuching',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Khandyga',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Samoa',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chatham',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kyiv',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kyiv',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Wayne',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tomsk',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Eastern',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Budapest',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Magadan',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Metlakatla',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jerusalem',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wake',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nairobi',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Astrakhan',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Malabo',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kirov',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Saipan',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Saskatchewan',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Hermosillo',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Maceio',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belfast',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-0',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Mawson',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Madrid',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\EasterIsland',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vatican',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Asuncion',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Brussels',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Christmas',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Knox',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Los_Angeles',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cordoba',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Kitts',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Conakry',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Vincent',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Yap',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\East',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tunis',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\El_Aaiun',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chicago',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kolkata',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('pytz\\zoneinfo\\MET',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\MET',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mogadishu',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-2',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Brazzaville',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Truk',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('pytz\\zoneinfo\\UTC',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+7',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Porto-Novo',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Irkutsk',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grand_Turk',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Swift_Current',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Menominee',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Pangnirtung',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('pytz\\zoneinfo\\ROK',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\ROK',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimphu',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashgabat',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qyzylorda',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Volgograd',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Adak',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Adak',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dacca',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bahrain',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ust-Nera',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bishkek',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Copenhagen',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dominica',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Malta',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Rangoon',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('pytz\\zoneinfo\\EET',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\EET',
   'DATA'),
  ('pytz\\zoneinfo\\Iran',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Iran',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kathmandu',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Paris',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Gaza',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mauritius',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Syowa',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ouagadougou',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Nauru',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\West',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+3',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Blantyre',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lindeman',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Comoro',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chita',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Noumea',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\General',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faroe',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Brisbane',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Rarotonga',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayenne',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tokyo',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-8',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('pytz\\zoneinfo\\GMT',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Broken_Hill',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('pytz\\zoneinfo\\leapseconds',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\leapseconds',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santarem',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Virgin',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('pytz\\zoneinfo\\tzdata.zi',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('pytz\\zoneinfo\\Portugal',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Portugal',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmara',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Norfolk',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qatar',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-4',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('pytz\\zoneinfo\\GB-Eire',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\GB-Eire',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Eirunepe',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Danmarkshavn',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+2',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atikokan',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Detroit',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anguilla',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-9',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Recife',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Recife',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mahe',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Halifax',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lome',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Vancouver',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bangui',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('pytz\\zoneinfo\\Jamaica',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\South_Pole',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Eucla',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Famagusta',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tashkent',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faeroe',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kigali',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('pytz\\zoneinfo\\Navajo',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Navajo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lubumbashi',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayman',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Easter',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\LHI',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Sakhalin',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Goose_Bay',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Cuba',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Cuba',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mbabane',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Nicosia',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Oslo',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Kerguelen',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Muscat',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Barthelemy',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port-au-Prince',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Manaus',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Bermuda',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Barbados',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kaliningrad',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Currie',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('pytz\\zoneinfo\\Libya',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Libya',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Edmonton',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Brunei',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baghdad',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtobe',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Chisinau',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Pacific',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Curacao',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belem',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Belem',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mendoza',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+12',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kralendijk',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Majuro',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kamchatka',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montserrat',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pyongyang',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Macquarie',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vilnius',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cuiaba',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Louisville',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Arizona',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('pytz\\zoneinfo\\Hongkong',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Hongkong',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sitka',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-3',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Johnston',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Johnston',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Freetown',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Lucia',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\NSW',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-6',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Singapore',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-1',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grenada',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Newfoundland',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Merida',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Merida',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maputo',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Atyrau',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tahiti',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuwait',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tegucigalpa',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('pytz\\zoneinfo\\EST',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\EST',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dubai',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Puerto_Rico',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Monterrey',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimbu',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('pytz\\zoneinfo\\zone1970.tab',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Maldives',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Gambier',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boise',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Boise',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Alaska',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jakarta',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UTC',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bucharest',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Berlin',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('pytz\\zoneinfo\\Eire',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Athens',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fakaofo',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Havana',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Havana',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\Continental',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson_Creek',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Enderbury',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aden',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Velho',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macau',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chuuk',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\North',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\North',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santo_Domingo',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Yukon',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Seoul',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Iqaluit',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jayapura',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rankin_Inlet',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Moscow',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Anadyr',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Marquesas',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indianapolis',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\GMT0',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+9',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dhaka',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nipigon',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-12',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('pytz\\zoneinfo\\W-SU',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\W-SU',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kampala',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vladivostok',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Rothera',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Samoa',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Costa_Rica',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+5',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Gaborone',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Andorra',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Uzhgorod',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montreal',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\St_Helena',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ensenada',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Aruba',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Saratov',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Prague',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-10',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Palmer',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('pytz\\zoneinfo\\PST8PDT',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\PST8PDT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dili',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tiraspol',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bratislava',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lagos',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kiev',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Regina',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Regina',
   'DATA'),
  ('pytz\\zoneinfo\\Israel',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Israel',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+4',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Timbuktu',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novosibirsk',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Palau',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mayotte',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kinshasa',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kosrae',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('pytz\\zoneinfo\\GMT+0',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chihuahua',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Rome',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-14',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Almaty',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yerevan',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\DeNoronha',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bujumbura',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Blanc-Sablon',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Troll',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Araguaina',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Mariehamn',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Auckland',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Libreville',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lower_Princes',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Campo_Grande',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Simferopol',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Honolulu',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sl.qm',
   'D:\\Anaconda\\Library\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'D:\\Anaconda\\Library\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'D:\\Anaconda\\Library\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'D:\\Anaconda\\Library\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'D:\\Anaconda\\Library\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pt.qm',
   'D:\\Anaconda\\Library\\translations\\qt_pt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'D:\\Anaconda\\Library\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'D:\\Anaconda\\Library\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'D:\\Anaconda\\Library\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'D:\\Anaconda\\Library\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'D:\\Anaconda\\Library\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'D:\\Anaconda\\Library\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'D:\\Anaconda\\Library\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ja.qm',
   'D:\\Anaconda\\Library\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'D:\\Anaconda\\Library\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'D:\\Anaconda\\Library\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_he.qm',
   'D:\\Anaconda\\Library\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'D:\\Anaconda\\Library\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'D:\\Anaconda\\Library\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'D:\\Anaconda\\Library\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gd.qm',
   'D:\\Anaconda\\Library\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fr.qm',
   'D:\\Anaconda\\Library\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ar.qm',
   'D:\\Anaconda\\Library\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sv.qm',
   'D:\\Anaconda\\Library\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'D:\\Anaconda\\Library\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'D:\\Anaconda\\Library\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lv.qm',
   'D:\\Anaconda\\Library\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'D:\\Anaconda\\Library\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_it.qm',
   'D:\\Anaconda\\Library\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_uk.qm',
   'D:\\Anaconda\\Library\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fa.qm',
   'D:\\Anaconda\\Library\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_de.qm',
   'D:\\Anaconda\\Library\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'D:\\Anaconda\\Library\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'D:\\Anaconda\\Library\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'D:\\Anaconda\\Library\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'D:\\Anaconda\\Library\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'D:\\Anaconda\\Library\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_da.qm',
   'D:\\Anaconda\\Library\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_en.qm',
   'D:\\Anaconda\\Library\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sk.qm',
   'D:\\Anaconda\\Library\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'D:\\Anaconda\\Library\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'D:\\Anaconda\\Library\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ko.qm',
   'D:\\Anaconda\\Library\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_tr.qm',
   'D:\\Anaconda\\Library\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_bg.qm',
   'D:\\Anaconda\\Library\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'D:\\Anaconda\\Library\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'D:\\Anaconda\\Library\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'D:\\Anaconda\\Library\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'D:\\Anaconda\\Library\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'D:\\Anaconda\\Library\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'D:\\Anaconda\\Library\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gl.qm',
   'D:\\Anaconda\\Library\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'D:\\Anaconda\\Library\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'D:\\Anaconda\\Library\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pl.qm',
   'D:\\Anaconda\\Library\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'D:\\Anaconda\\Library\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_es.qm',
   'D:\\Anaconda\\Library\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ca.qm',
   'D:\\Anaconda\\Library\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'D:\\Anaconda\\Library\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fi.qm',
   'D:\\Anaconda\\Library\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'D:\\Anaconda\\Library\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lt.qm',
   'D:\\Anaconda\\Library\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_cs.qm',
   'D:\\Anaconda\\Library\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'D:\\Anaconda\\Library\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'D:\\Anaconda\\Library\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'D:\\Anaconda\\Library\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'D:\\Anaconda\\Library\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'D:\\Anaconda\\Library\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'D:\\Anaconda\\Library\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_hu.qm',
   'D:\\Anaconda\\Library\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ru.qm',
   'D:\\Anaconda\\Library\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'D:\\Anaconda\\Library\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'D:\\Anaconda\\Library\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'D:\\Anaconda\\Library\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'D:\\Anaconda\\Library\\translations\\qtbase_pl.qm',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\METADATA',
   'D:\\Anaconda\\Lib\\site-packages\\pyreadline3-3.5.4.dist-info\\METADATA',
   'DATA'),
  ('wheel-0.38.4-py3.11.egg-info\\top_level.txt',
   'D:\\Anaconda\\Lib\\site-packages\\wheel-0.38.4-py3.11.egg-info\\top_level.txt',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\RECORD',
   'D:\\Anaconda\\Lib\\site-packages\\pyreadline3-3.5.4.dist-info\\RECORD',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\INSTALLER',
   'D:\\Anaconda\\Lib\\site-packages\\pyreadline3-3.5.4.dist-info\\INSTALLER',
   'DATA'),
  ('wheel-0.38.4-py3.11.egg-info\\not-zip-safe',
   'D:\\Anaconda\\Lib\\site-packages\\wheel-0.38.4-py3.11.egg-info\\not-zip-safe',
   'DATA'),
  ('wheel-0.38.4-py3.11.egg-info\\requires.txt',
   'D:\\Anaconda\\Lib\\site-packages\\wheel-0.38.4-py3.11.egg-info\\requires.txt',
   'DATA'),
  ('wheel-0.38.4-py3.11.egg-info\\SOURCES.txt',
   'D:\\Anaconda\\Lib\\site-packages\\wheel-0.38.4-py3.11.egg-info\\SOURCES.txt',
   'DATA'),
  ('wheel-0.38.4-py3.11.egg-info\\entry_points.txt',
   'D:\\Anaconda\\Lib\\site-packages\\wheel-0.38.4-py3.11.egg-info\\entry_points.txt',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\top_level.txt',
   'D:\\Anaconda\\Lib\\site-packages\\pyreadline3-3.5.4.dist-info\\top_level.txt',
   'DATA'),
  ('wheel-0.38.4-py3.11.egg-info\\PKG-INFO',
   'D:\\Anaconda\\Lib\\site-packages\\wheel-0.38.4-py3.11.egg-info\\PKG-INFO',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\WHEEL',
   'D:\\Anaconda\\Lib\\site-packages\\pyreadline3-3.5.4.dist-info\\WHEEL',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\LICENSE.md',
   'D:\\Anaconda\\Lib\\site-packages\\pyreadline3-3.5.4.dist-info\\LICENSE.md',
   'DATA'),
  ('wheel-0.38.4-py3.11.egg-info\\dependency_links.txt',
   'D:\\Anaconda\\Lib\\site-packages\\wheel-0.38.4-py3.11.egg-info\\dependency_links.txt',
   'DATA'),
  ('base_library.zip',
   'E:\\TLabel\\FindAbsence\\build\\attendance_simple\\base_library.zip',
   'DATA')],
 'python311.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
