
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named 'org.python' - imported by copy (optional), xml.sax (delayed, conditional)
missing module named org - imported by pickle (optional)
missing module named pwd - imported by posixpath (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional), http.server (delayed, optional), webbrowser (delayed), psutil (optional), netrc (delayed, conditional), getpass (delayed), distutils.util (delayed, conditional, optional), distutils.archive_util (optional), _pytest._py.path (delayed), twisted.python.util (optional), setuptools._distutils.util (delayed, conditional, optional), setuptools._distutils.archive_util (optional)
missing module named grp - imported by shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional), distutils.archive_util (optional), _pytest._py.path (delayed), twisted.python.util (optional), setuptools._distutils.archive_util (optional)
missing module named posix - imported by os (conditional, optional), posixpath (optional), shutil (conditional), importlib._bootstrap_external (conditional)
missing module named resource - imported by posix (top-level), ptyprocess.ptyprocess (top-level), fsspec.asyn (conditional, optional), twisted.internet.process (delayed, optional)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named pyimod02_importers - imported by D:\Anaconda\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed), D:\Anaconda\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgres.py (delayed)
missing module named _posixsubprocess - imported by subprocess (conditional), multiprocessing.util (delayed)
missing module named fcntl - imported by subprocess (optional), psutil._compat (delayed, optional), tqdm.utils (delayed, optional), xmlrpc.server (optional), pty (delayed, optional), ptyprocess.ptyprocess (top-level), twisted.python.compat (delayed, optional), twisted.internet.fdesc (optional), twisted.internet.process (optional)
missing module named _manylinux - imported by packaging._manylinux (delayed, optional), setuptools._vendor.packaging._manylinux (delayed, optional), pkg_resources._vendor.packaging._manylinux (delayed, optional)
missing module named jnius - imported by platformdirs.android (delayed, optional), pkg_resources._vendor.platformdirs.android (delayed, optional)
missing module named 'pkg_resources.extern.importlib_resources' - imported by pkg_resources._vendor.jaraco.text (optional)
missing module named 'pkg_resources.extern.more_itertools' - imported by pkg_resources._vendor.jaraco.functools (top-level)
missing module named pkg_resources.extern.packaging - imported by pkg_resources.extern (top-level), pkg_resources (top-level)
missing module named pkg_resources.extern.platformdirs - imported by pkg_resources.extern (top-level), pkg_resources (top-level)
missing module named 'pkg_resources.extern.jaraco' - imported by pkg_resources (top-level), pkg_resources._vendor.jaraco.text (top-level)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named termios - imported by tty (top-level), psutil._compat (delayed, optional), getpass (optional), tqdm.utils (delayed, optional), ptyprocess.ptyprocess (top-level), twisted.internet.process (optional)
missing module named 'java.lang' - imported by platform (delayed, optional), xml.sax._exceptions (conditional)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
missing module named _winreg - imported by platform (delayed, optional), numexpr.cpuinfo (delayed, optional)
missing module named 'distutils._log' - imported by setuptools._distutils.command.bdist_dumb (top-level), setuptools._distutils.command.bdist_rpm (top-level), setuptools._distutils.command.build_clib (top-level), setuptools._distutils.command.build_ext (top-level), setuptools._distutils.command.build_py (top-level), setuptools._distutils.command.build_scripts (top-level), setuptools._distutils.command.clean (top-level), setuptools._distutils.command.config (top-level), setuptools._distutils.command.install (top-level), setuptools._distutils.command.install_scripts (top-level), setuptools._distutils.command.register (top-level), setuptools._distutils.command.sdist (top-level)
missing module named usercustomize - imported by site (delayed, optional)
missing module named sitecustomize - imported by site (delayed, optional)
missing module named startup - imported by pyreadline3.keysyms.common (conditional), pyreadline3.keysyms.keysyms (conditional)
missing module named sets - imported by pyreadline3.keysyms.common (optional), pytz.tzinfo (optional)
missing module named System - imported by pyreadline3.clipboard.ironpython_clipboard (top-level), pyreadline3.keysyms.ironpython_keysyms (top-level), pyreadline3.console.ironpython_console (top-level), pyreadline3.rlmain (conditional)
missing module named console - imported by pyreadline3.console.ansi (conditional)
missing module named clr - imported by pyreadline3.clipboard.ironpython_clipboard (top-level), pyreadline3.console.ironpython_console (top-level)
missing module named IronPythonConsole - imported by pyreadline3.console.ironpython_console (top-level)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level), numba.testing.main (optional)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.Process - imported by multiprocessing (top-level), jupyter_client.ssh.tunnel (top-level)
missing module named multiprocessing.freeze_support - imported by multiprocessing (conditional), numba.runtests (conditional)
missing module named multiprocessing.RLock - imported by multiprocessing (delayed, conditional, optional), tqdm.std (delayed, conditional, optional)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named collections.Sequence - imported by collections (optional), setuptools._vendor.ordered_set (optional)
missing module named collections.MutableSet - imported by collections (optional), setuptools._vendor.ordered_set (optional)
missing module named 'setuptools.extern.jaraco' - imported by setuptools._reqs (top-level), setuptools._entry_points (top-level), setuptools.command.egg_info (top-level), setuptools._vendor.jaraco.text (top-level)
missing module named setuptools.extern.importlib_resources - imported by setuptools.extern (conditional), setuptools._importlib (conditional), setuptools._vendor.jaraco.text (optional)
missing module named setuptools.extern.tomli - imported by setuptools.extern (delayed), setuptools.config.pyprojecttoml (delayed)
missing module named setuptools.extern.ordered_set - imported by setuptools.extern (top-level), setuptools.dist (top-level)
missing module named setuptools.extern.packaging - imported by setuptools.extern (top-level), setuptools.dist (top-level), setuptools._normalization (top-level), setuptools.command.egg_info (top-level), setuptools.depends (top-level)
missing module named setuptools.extern.importlib_metadata - imported by setuptools.extern (conditional), setuptools._importlib (conditional)
missing module named 'setuptools.extern.more_itertools' - imported by setuptools.dist (top-level), setuptools.config.expand (delayed), setuptools.config.pyprojecttoml (delayed), setuptools._itertools (top-level), setuptools._entry_points (top-level), setuptools.msvc (top-level), setuptools._vendor.jaraco.functools (top-level)
missing module named 'setuptools.extern.packaging.utils' - imported by setuptools.wheel (top-level)
missing module named 'setuptools.extern.packaging.tags' - imported by setuptools.wheel (top-level)
missing module named 'setuptools.extern.packaging.version' - imported by setuptools.config.setupcfg (top-level), setuptools.wheel (top-level)
missing module named trove_classifiers - imported by setuptools.config._validate_pyproject.formats (optional)
missing module named 'setuptools.extern.packaging.specifiers' - imported by setuptools.config.setupcfg (top-level), setuptools.config._apply_pyprojecttoml (delayed)
missing module named 'setuptools.extern.packaging.requirements' - imported by setuptools.config.setupcfg (top-level), setuptools._reqs (top-level)
missing module named 'setuptools.extern.packaging.markers' - imported by setuptools.config.setupcfg (top-level)
missing module named dl - imported by setuptools.command.build_ext (conditional, optional)
missing module named Cython - imported by setuptools.command.build_ext (optional)
missing module named xmlrpclib - imported by defusedxml.xmlrpc (conditional)
missing module named 'lxml.etree' - imported by openpyxl.xml (delayed, optional), openpyxl.xml.functions (conditional), pandas.io.xml (delayed, conditional), pandas.io.formats.xml (delayed), pandas.io.html (delayed)
excluded module named PIL.ImageTk - imported by PIL (delayed), PIL.SpiderImagePlugin (delayed)
missing module named olefile - imported by PIL.FpxImagePlugin (top-level), PIL.MicImagePlugin (top-level)
excluded module named PIL.ImageQt - imported by PIL (delayed), PIL.Image (delayed)
missing module named PIL._imagingcms - imported by PIL (optional), PIL.ImageCms (optional)
missing module named numpy.conj - imported by numpy (top-level), numexpr.tests.test_numexpr (top-level)
missing module named numpy.expm1 - imported by numpy (top-level), numexpr.tests.test_numexpr (top-level)
missing module named numpy.log1p - imported by numpy (top-level), numexpr.tests.test_numexpr (top-level)
missing module named numpy.log - imported by numpy (top-level), numexpr.tests.test_numexpr (top-level)
missing module named numpy.arctanh - imported by numpy (top-level), numexpr.tests.test_numexpr (top-level)
missing module named numpy.arccosh - imported by numpy (top-level), numexpr.tests.test_numexpr (top-level)
missing module named numpy.arcsinh - imported by numpy (top-level), numexpr.tests.test_numexpr (top-level)
missing module named numpy.tanh - imported by numpy (top-level), numexpr.tests.test_numexpr (top-level)
missing module named numpy.cosh - imported by numpy (top-level), numexpr.tests.test_numexpr (top-level)
missing module named numpy.sinh - imported by numpy (top-level), numexpr.tests.test_numexpr (top-level)
missing module named numpy.arctan - imported by numpy (top-level), numexpr.tests.test_numexpr (top-level)
missing module named numpy.arccos - imported by numpy (top-level), numexpr.tests.test_numexpr (top-level)
missing module named numpy.arcsin - imported by numpy (top-level), numexpr.tests.test_numexpr (top-level)
missing module named numpy.tan - imported by numpy (top-level), numexpr.tests.test_numexpr (top-level)
missing module named numpy.ceil - imported by numpy (top-level), numexpr.tests.test_numexpr (top-level)
missing module named numpy.floor - imported by numpy (top-level), numexpr.tests.test_numexpr (top-level)
missing module named numpy.fmod - imported by numpy (top-level), numexpr.tests.test_numexpr (top-level)
missing module named dummy_threading - imported by psutil._compat (optional), requests.cookies (optional)
missing module named _dummy_thread - imported by numpy.core.arrayprint (optional), cffi.lock (conditional, optional)
missing module named numpy.core.result_type - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.float_ - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.number - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.object_ - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.all - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.bool_ - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.inf - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.array2string - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.lib.imag - imported by numpy.lib (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.lib.real - imported by numpy.lib (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.lib.iscomplexobj - imported by numpy.lib (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.signbit - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.isscalar - imported by numpy.core (delayed), numpy.testing._private.utils (delayed), numpy.lib.polynomial (top-level)
missing module named numpy.core.isinf - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.errstate - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.isfinite - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.isnan - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.array - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.isnat - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.ndarray - imported by numpy.core (top-level), numpy.testing._private.utils (top-level), numpy.lib.utils (top-level)
missing module named numpy.core.array_repr - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.arange - imported by numpy.core (top-level), numpy.testing._private.utils (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.empty - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.float32 - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.intp - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.linspace - imported by numpy.core (top-level), numpy.lib.index_tricks (top-level)
missing module named numpy.core.iinfo - imported by numpy.core (top-level), numpy.lib.twodim_base (top-level)
missing module named numpy.core.transpose - imported by numpy.core (top-level), numpy.lib.function_base (top-level)
missing module named numpy._typing._ufunc - imported by numpy._typing (conditional)
excluded module named numpy.random._bounded_integers - imported by numpy.random (top-level)
excluded module named numpy.random._pickle - imported by numpy.random (top-level)
missing module named numpy.uint - imported by numpy (top-level), numpy.random._generator (top-level), numpy.random.mtrand (top-level)
missing module named numpy.core.asarray - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.utils (top-level), numpy.fft._pocketfft (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.integer - imported by numpy.core (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.sqrt - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.conjugate - imported by numpy.core (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.swapaxes - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.zeros - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.reciprocal - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sort - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.argsort - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sign - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.count_nonzero - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.divide - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.matmul - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.asanyarray - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.atleast_2d - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.product - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.amax - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.amin - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.moveaxis - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.geterrobj - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.finfo - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.sum - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.multiply - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.add - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.dot - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.Inf - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.newaxis - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.complexfloating - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.inexact - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.cdouble - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.csingle - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.double - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.single - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.intc - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.empty_like - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.ufunc - imported by numpy.core (top-level), numpy.lib.utils (top-level)
missing module named numpy.core.ones - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.hstack - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.atleast_1d - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.atleast_3d - imported by numpy.core (top-level), numpy.lib.shape_base (top-level)
missing module named numpy.core.vstack - imported by numpy.core (top-level), numpy.lib.shape_base (top-level)
missing module named pickle5 - imported by numpy.compat.py3k (optional), numba.cloudpickle.compat (conditional, optional), cloudpickle.compat (conditional, optional)
missing module named numpy.eye - imported by numpy (delayed), numpy.core.numeric (delayed)
missing module named numpy.recarray - imported by numpy (top-level), numpy.ma.mrecords (top-level)
missing module named numpy.expand_dims - imported by numpy (top-level), numpy.ma.core (top-level)
missing module named numpy.array - imported by numpy (top-level), numpy.ma.core (top-level), numpy.ma.extras (top-level), numpy.ma.mrecords (top-level), numba.cuda.vectorizers (top-level), numexpr.tests.test_numexpr (top-level), dill._objects (optional)
missing module named numpy.iscomplexobj - imported by numpy (top-level), numpy.ma.core (top-level)
missing module named numpy.amin - imported by numpy (top-level), numpy.ma.core (top-level)
missing module named numpy.amax - imported by numpy (top-level), numpy.ma.core (top-level)
missing module named numpy.float64 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random._generator (top-level), numpy.random.mtrand (top-level), numexpr.tests.test_numexpr (top-level)
missing module named numpy.float32 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random._generator (top-level), numpy.random.mtrand (top-level)
missing module named numpy.uint64 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random._generator (top-level), numpy.random.bit_generator (top-level), numpy.random._philox (top-level), numpy.random._sfc64 (top-level), numpy.random.mtrand (top-level)
missing module named numpy.uint32 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random._generator (top-level), numpy.random.bit_generator (top-level), numpy.random._mt19937 (top-level), numpy.random.mtrand (top-level)
missing module named numpy.uint16 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random._generator (top-level), numpy.random.mtrand (top-level), numexpr.tests.test_numexpr (top-level)
missing module named numpy.uint8 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random._generator (top-level), numpy.random.mtrand (top-level)
missing module named numpy.int64 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random._generator (top-level), numpy.random.mtrand (top-level), numexpr.tests.test_numexpr (top-level)
missing module named numpy.int32 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random._generator (top-level), numpy.random.mtrand (top-level), numexpr.tests.test_numexpr (top-level), dill._objects (optional)
missing module named numpy.int16 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random._generator (top-level), numpy.random.mtrand (top-level)
missing module named numpy.int8 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random._generator (top-level), numpy.random.mtrand (top-level)
missing module named numpy.bytes_ - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.str_ - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.void - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.object_ - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.datetime64 - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.timedelta64 - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.number - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.complexfloating - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.floating - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.integer - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.ctypeslib (top-level)
missing module named numpy.unsignedinteger - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.bool_ - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.ma.core (top-level), numpy.ma.mrecords (top-level), numpy.random._generator (top-level), numpy.random.mtrand (top-level)
missing module named numpy.generic - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.dtype - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.array_api._typing (top-level), numpy.ma.mrecords (top-level), numpy.random._generator (top-level), numpy.random.bit_generator (top-level), numpy.random._mt19937 (top-level), numpy.random._philox (top-level), numpy.random._sfc64 (top-level), numpy.random.mtrand (top-level), numpy.ctypeslib (top-level), dill._dill (delayed)
missing module named numpy.ndarray - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.ma.core (top-level), numpy.ma.extras (top-level), numpy.ma.mrecords (top-level), numpy.random._generator (top-level), numpy.random.bit_generator (top-level), numpy.random._mt19937 (top-level), numpy.random._philox (top-level), numpy.random._sfc64 (top-level), numpy.random.mtrand (top-level), numpy.ctypeslib (top-level), pandas.compat.numpy.function (top-level), dill._dill (delayed), _pytest.python_api (conditional)
missing module named numpy.ufunc - imported by numpy (top-level), numpy._typing (top-level), dill._dill (delayed), dill._objects (optional)
missing module named numpy.histogramdd - imported by numpy (delayed), numpy.lib.twodim_base (delayed)
missing module named collections.Callable - imported by collections (optional), cffi.api (optional), bs4.element (optional), bs4.builder._lxml (optional), socks (optional)
missing module named dummy_thread - imported by cffi.lock (conditional, optional)
missing module named thread - imported by cffi.lock (conditional, optional), cffi.cparser (conditional, optional)
missing module named cStringIO - imported by cffi.ffiplatform (optional), xlrd.timemachine (conditional)
missing module named cPickle - imported by pycparser.ply.yacc (delayed, optional)
missing module named cffi._pycparser - imported by cffi (optional), cffi.cparser (optional)
missing module named openpyxl.tests - imported by openpyxl.reader.excel (optional)
missing module named StringIO - imported by six (conditional), urllib3.packages.six (conditional)
missing module named six.moves.range - imported by six.moves (top-level), dateutil.rrule (top-level)
runtime module named six.moves - imported by dateutil.tz.tz (top-level), dateutil.tz._factories (top-level), dateutil.tz.win (top-level), dateutil.rrule (top-level)
missing module named dateutil.tz.tzfile - imported by dateutil.tz (top-level), dateutil.zoneinfo (top-level)
missing module named 'sphinx.ext' - imported by pyarrow.vendored.docscrape (delayed, conditional)
missing module named 'pyarrow._substrait' - imported by pyarrow.substrait (top-level)
excluded module named tensorflow - imported by pyarrow.plasma (delayed, optional), pyarrow.conftest (optional)
missing module named 'pyarrow._plasma' - imported by pyarrow.plasma (top-level)
missing module named pyarrow.parquet.ParquetDataset - imported by pyarrow.parquet (delayed), pyarrow.filesystem (delayed)
missing module named 'pyarrow._cuda' - imported by pyarrow.cuda (top-level)
missing module named 'pyarrow.gandiva' - imported by pyarrow.conftest (optional)
missing module named fastparquet - imported by fsspec.parquet (delayed), pyarrow.conftest (optional)
missing module named cython - imported by pyarrow.conftest (optional)
excluded module named pywintypes - imported by twisted.python.lockfile (conditional, optional), twisted.internet._dumbwin32proc (top-level), twisted.internet._pollingfile (top-level), twisted.trial.reporter (delayed)
missing module named _curses - imported by curses (top-level), curses.has_key (top-level)
missing module named subunit - imported by twisted.trial.reporter (optional)
missing module named 'cryptography.x509' - imported by OpenSSL.crypto (delayed), urllib3.contrib.pyopenssl (delayed, optional), service_identity.cryptography (top-level)
missing module named 'cryptography.hazmat' - imported by OpenSSL._util (top-level), OpenSSL.crypto (top-level), urllib3.contrib.pyopenssl (top-level)
excluded module named cryptography - imported by OpenSSL.crypto (top-level), urllib3.contrib.pyopenssl (top-level), requests (conditional, optional)
missing module named '_pytest.code' - imported by _pytest.hookspec (conditional)
missing module named 'pygments.util' - imported by _pytest._io.terminalwriter (delayed, optional)
excluded module named pygments - imported by numba.core.ir (delayed, conditional, optional), numba.core.lowering (delayed, conditional, optional), numba.core.codegen (delayed, conditional, optional), numba.core.annotations.pretty_annotate (delayed, optional), _pytest._io.terminalwriter (delayed, optional)
missing module named 'pygments.lexers' - imported by numba.core.lowering (delayed, conditional, optional), numba.core.codegen (delayed, conditional, optional), numba.core.annotations.pretty_annotate (delayed, optional), _pytest._io.terminalwriter (delayed, optional)
missing module named 'pygments.formatters' - imported by numba.core.ir (delayed, conditional, optional), numba.core.lowering (delayed, conditional, optional), numba.core.codegen (delayed, conditional, optional), numba.core.annotations.pretty_annotate (delayed, optional), _pytest._io.terminalwriter (delayed, optional)
missing module named exceptiongroup - imported by _pytest.runner (conditional), _pytest._code.code (conditional)
missing module named argcomplete - imported by _pytest._argcomplete (conditional, optional)
missing module named __builtin__ - imported by ptyprocess.ptyprocess (optional), debugpy.common.compat (conditional)
missing module named sparse - imported by pyarrow.serialization (delayed, optional)
missing module named 'scipy.sparse' - imported by pandas.core.arrays.sparse.array (conditional), pandas.core.arrays.sparse.scipy_sparse (delayed, conditional), pandas.core.arrays.sparse.accessor (delayed), pyarrow.serialization (delayed, optional), pandas.core.dtypes.common (delayed, conditional, optional)
excluded module named torch - imported by pyarrow.serialization (delayed, optional)
excluded module named distributed - imported by fsspec.transaction (delayed)
missing module named urllib3_secure_extra - imported by urllib3 (optional)
missing module named brotlicffi - imported by urllib3.util.request (optional), urllib3.response (optional)
runtime module named urllib3.packages.six.moves - imported by http.client (top-level), urllib3.util.response (top-level), urllib3.connectionpool (top-level), urllib3.packages.six.moves.urllib (top-level), urllib3.util.queue (top-level)
missing module named collections.MutableMapping - imported by collections (optional), urllib3._collections (optional)
missing module named collections.Mapping - imported by collections (optional), pytz.lazy (optional), urllib3._collections (optional)
missing module named 'urllib3.packages.six.moves.urllib.parse' - imported by urllib3.request (top-level), urllib3.poolmanager (top-level)
missing module named Queue - imported by numba.testing.main (optional), debugpy.common.compat (conditional), urllib3.util.queue (conditional)
missing module named simplejson - imported by requests.compat (conditional, optional)
missing module named unicodedata2 - imported by charset_normalizer.utils (optional)
missing module named requests_kerberos - imported by fsspec.implementations.webhdfs (delayed, conditional)
missing module named smbclient - imported by fsspec.implementations.smb (top-level)
excluded module named paramiko - imported by jupyter_client.ssh.tunnel (optional), fsspec.implementations.sftp (top-level)
excluded module named jinja2 - imported by numba.core.annotations.type_annotations (delayed, optional), numba.core.annotations.pretty_annotate (delayed, optional), fsspec.implementations.reference (delayed, conditional), pandas.io.formats.style (top-level)
missing module named kerchunk - imported by fsspec.implementations.reference (delayed)
missing module named tokio - imported by aiohttp.worker (delayed)
missing module named uvloop - imported by aiohttp.worker (delayed)
missing module named 'gunicorn.workers' - imported by aiohttp.worker (top-level)
missing module named gunicorn - imported by aiohttp.worker (top-level)
missing module named cchardet - imported by bs4.dammit (optional), aiohttp.client_reqrep (optional)
missing module named aiodns - imported by aiohttp.resolver (optional)
missing module named idna_ssl - imported by aiohttp.helpers (conditional)
missing module named pygit2 - imported by fsspec.implementations.git (top-level)
missing module named 'distributed.worker' - imported by fsspec.implementations.dask (top-level)
missing module named 'distributed.client' - imported by fsspec.implementations.dask (top-level)
excluded module named dask - imported by fsspec.implementations.dask (top-level)
excluded module named panel - imported by fsspec.gui (top-level)
missing module named fuse - imported by fsspec.fuse (top-level)
missing module named zstandard.backend_rust - imported by zstandard (conditional)
missing module named snappy._snappy_cffi - imported by snappy.snappy_cffi (top-level)
missing module named lzmaffi - imported by fsspec.compression (optional)
missing module named isal - imported by fsspec.compression (optional)
missing module named 'IPython.display' - imported by llvmlite.binding.analysis (delayed, conditional, optional), ipykernel.zmqshell (top-level), ipywidgets.widgets.widget_output (top-level), ipywidgets.widgets.interaction (top-level), matplotlib_inline.backend_inline (top-level), tqdm.notebook (conditional, optional)
missing module named 'IPython.html' - imported by tqdm.notebook (conditional, optional)
missing module named trio - imported by ipykernel.trio_runner (top-level)
missing module named 'gi.repository' - imported by ipykernel.gui.gtk3embed (top-level)
missing module named gi - imported by ipykernel.gui.gtk3embed (top-level)
missing module named gtk - imported by ipykernel.gui.gtkembed (top-level)
missing module named gobject - imported by ipykernel.gui.gtkembed (top-level)
excluded module named tkinter - imported by ipykernel.eventloops (delayed)
missing module named wx - imported by ipykernel.eventloops (delayed)
missing module named PySide2 - imported by ipykernel.eventloops (delayed, conditional, optional)
missing module named 'IPython.lib' - imported by ipykernel.eventloops (delayed)
missing module named 'IPython.external' - imported by ipykernel.eventloops (delayed)
missing module named traitlets.config.Application - imported by traitlets.config (delayed, conditional), traitlets.log (delayed, conditional), ipykernel.kernelspec (top-level)
missing module named ipyparallel - imported by ipykernel.zmqshell (delayed, conditional)
missing module named 'IPython.utils' - imported by ipykernel.ipkernel (top-level), ipykernel.zmqshell (top-level)
missing module named _subprocess - imported by jupyter_client.launcher (delayed, conditional, optional), ipykernel.parentpoller (delayed, optional)
missing module named 'ipyparallel.serialize' - imported by ipykernel.ipkernel (delayed, optional), ipykernel.serialize (optional), ipykernel.pickleutil (top-level)
missing module named appnope - imported by ipykernel.ipkernel (delayed, conditional)
missing module named '_pydevd_bundle.pydevd_api' - imported by ipykernel.debugger (delayed)
missing module named '_pydevd_bundle.pydevd_suspended_frames' - imported by ipykernel.debugger (optional)
missing module named _pydevd_bundle - imported by debugpy._vendored.force_pydevd (top-level), ipykernel.debugger (optional)
missing module named pydevd_file_utils - imported by debugpy.server.api (top-level)
missing module named '_pydevd_bundle.pydevd_constants' - imported by debugpy.server.api (top-level)
missing module named pydevd - imported by debugpy._vendored.force_pydevd (top-level), debugpy.server.api (top-level)
missing module named 'zmq.utils' - imported by ipykernel.debugger (top-level)
missing module named netifaces - imported by jupyter_client.localinterfaces (delayed)
missing module named 'zmq.eventloop' - imported by jupyter_client.session (top-level), ipykernel.kernelapp (top-level), ipykernel.iostream (top-level), ipykernel.ipkernel (top-level), ipykernel.kernelbase (top-level)
missing module named 'zmq.asyncio' - imported by jupyter_client.asynchronous.client (top-level), jupyter_client.channels (top-level), jupyter_client.session (top-level), jupyter_client.client (top-level)
missing module named 'IPython.core' - imported by pandas.io.formats.printing (delayed, conditional), ipykernel.debugger (top-level), ipykernel.compiler (top-level), ipykernel.kernelapp (top-level), ipykernel.ipkernel (top-level), ipykernel.kernelbase (top-level), ipykernel.zmqshell (top-level), ipykernel.displayhook (top-level), ipywidgets.widgets.widget_output (top-level), matplotlib_inline.backend_inline (top-level), matplotlib_inline.config (delayed, conditional)
excluded module named zmq - imported by jupyter_client.connect (top-level), jupyter_client.manager (top-level), ipykernel.debugger (top-level), jupyter_client.multikernelmanager (top-level), ipykernel.kernelapp (top-level), ipykernel.heartbeat (top-level), ipykernel.iostream (top-level), ipykernel.kernelbase (top-level), ipykernel.eventloops (top-level)
missing module named 'IPython.paths' - imported by jupyter_client.kernelspec (delayed, optional)
missing module named _dbm - imported by dbm.ndbm (top-level)
missing module named _gdbm - imported by dbm.gnu (top-level)
missing module named diff - imported by dill._dill (delayed, conditional, optional)
missing module named dill.diff - imported by dill (delayed, conditional, optional), dill._dill (delayed, conditional, optional)
missing module named version - imported by dill (optional)
missing module named ipykernel.get_connection_info - imported by ipykernel (top-level), ipykernel.zmqshell (top-level)
missing module named ipykernel.get_connection_file - imported by ipykernel (top-level), ipykernel.zmqshell (top-level)
missing module named ipykernel.connect_qtconsole - imported by ipykernel (top-level), ipykernel.zmqshell (top-level)
missing module named 'matplotlib.figure' - imported by pandas.plotting._misc (conditional), pandas.plotting._matplotlib.tools (conditional), pandas.plotting._matplotlib.misc (conditional), matplotlib_inline.backend_inline (top-level)
missing module named 'matplotlib._pylab_helpers' - imported by matplotlib_inline.backend_inline (top-level)
missing module named 'matplotlib.backends' - imported by matplotlib_inline.backend_inline (top-level)
excluded module named matplotlib - imported by pandas.io.formats.style (optional), pandas.plotting._matplotlib.core (top-level), pandas.plotting._matplotlib.compat (delayed, optional), pandas.plotting._matplotlib.style (top-level), pandas.plotting._matplotlib.timeseries (delayed), tqdm.gui (delayed), ipywidgets.widgets.interaction (delayed, optional), matplotlib_inline.backend_inline (top-level)
excluded module named IPython - imported by pandas.io.formats.printing (delayed), ipywidgets (top-level), ipywidgets.widgets.widget (top-level), jupyter_client.client (delayed, conditional), comm.base_comm (delayed, conditional), ipywidgets.widgets.widget_output (top-level), ipywidgets.widgets.interaction (top-level)
missing module named pandas.core.groupby.PanelGroupBy - imported by pandas.core.groupby (delayed, optional), tqdm.std (delayed, optional)
missing module named graphviz - imported by llvmlite.binding.analysis (delayed), numba.core.ir (delayed, optional), numba.core.controlflow (delayed, optional), numba.misc.inspection (delayed, optional), numba.core.codegen (delayed)
missing module named llvmlite.ir.Value - imported by llvmlite.ir (delayed), llvmlite.ir.types (delayed)
missing module named llvmlite.ir.Constant - imported by llvmlite.ir (delayed), llvmlite.ir.types (delayed), numba.core.base (top-level), numba.core.pythonapi (top-level), numba.core.lowering (top-level), numba.core.generators (top-level), numba.core.callwrapper (top-level), numba.pycc.llvm_types (top-level), numba.np.npdatetime (top-level), numba.np.arrayobj (top-level), numba.cpython.mathimpl (top-level), numba.cpython.numbers (top-level), numba.cpython.unicode (top-level), numba.np.ufunc.wrappers (top-level)
missing module named llvmlite.ir.GlobalVariable - imported by llvmlite.ir (delayed), llvmlite.ir.types (delayed)
missing module named llvmlite.ir.Module - imported by llvmlite.ir (delayed), llvmlite.ir.types (delayed)
missing module named 'elftools.elf' - imported by numba.core.codegen (delayed)
missing module named elftools - imported by numba.core.codegen (delayed)
missing module named numba.typed.List - imported by numba.typed (delayed), numba.core.typing.typeof (delayed), numba.core.codegen (delayed), numba.typed.listobject (delayed)
missing module named numba.core.types.NoneType - imported by numba.core.types (top-level), numba.typed.listobject (top-level)
missing module named numba.core.types.Type - imported by numba.core.types (top-level), numba.typed.listobject (top-level), numba.typed.dictobject (top-level)
missing module named numba.core.types.ListTypeIteratorType - imported by numba.core.types (top-level), numba.typed.listobject (top-level)
missing module named numba.core.types.ListTypeIterableType - imported by numba.core.types (top-level), numba.typed.listobject (top-level)
missing module named numba.core.types.ListType - imported by numba.core.types (top-level), numba.typed.listobject (top-level), numba.typed.typedlist (top-level)
missing module named numba.core.types.WrapperAddressProtocol - imported by numba.core.types (top-level), numba.experimental.function_type (top-level)
missing module named numba.core.types.FunctionPrototype - imported by numba.core.types (top-level), numba.experimental.function_type (top-level)
missing module named numba.core.types.UndefinedFunctionType - imported by numba.core.types (top-level), numba.experimental.function_type (top-level)
missing module named numba.core.types.FunctionType - imported by numba.core.types (top-level), numba.experimental.function_type (top-level)
missing module named 'com.sun' - imported by numba.misc.appdirs (delayed, conditional, optional)
missing module named com - imported by numba.misc.appdirs (delayed)
missing module named 'win32com.shell' - imported by numba.misc.appdirs (delayed, conditional, optional)
missing module named numba.typed.Dict - imported by numba.typed (delayed), numba.core.typing.typeof (delayed), numba.typed.dictobject (delayed, conditional), numba.typed.dictimpl (delayed)
missing module named numba.core.types.Tuple - imported by numba.core.types (delayed), numba.core.types.iterators (delayed), numba.core.types.npytypes (delayed)
missing module named numba.core.types.Array - imported by numba.core.types (delayed), numba.core.types.abstract (delayed)
missing module named numba.core.types.StringLiteral - imported by numba.core.types (top-level), numba.np.arrayobj (top-level)
missing module named 'pygments.style' - imported by numba.misc.dump_style (top-level)
missing module named 'pygments.token' - imported by numba.misc.dump_style (top-level)
missing module named 'pygments.lexer' - imported by numba.misc.dump_style (top-level)
missing module named 'pygments.styles' - imported by numba.misc.dump_style (top-level)
missing module named r2pipe - imported by numba.misc.inspection (delayed, optional)
missing module named coverage - imported by numba.tests.support (optional)
missing module named numba.core.types.DictIteratorType - imported by numba.core.types (top-level), numba.typed.dictobject (top-level)
missing module named numba.core.types.DictValuesIterableType - imported by numba.core.types (top-level), numba.typed.dictobject (top-level)
missing module named numba.core.types.DictKeysIterableType - imported by numba.core.types (top-level), numba.typed.dictobject (top-level)
missing module named numba.core.types.DictItemsIterableType - imported by numba.core.types (top-level), numba.typed.dictobject (top-level)
missing module named numba.core.types.DictType - imported by numba.core.types (top-level), numba.typed.typeddict (top-level), numba.typed.dictobject (top-level)
missing module named numba.core.types.ClassInstanceType - imported by numba.core.types (top-level), numba.experimental.jitclass.overloads (top-level)
missing module named xmlrunner - imported by numba.testing (delayed, conditional)
missing module named git - imported by numba.testing.main (delayed, optional)
missing module named numba.cuda.is_available - imported by numba.cuda (delayed), numba.cuda.testing (delayed)
missing module named cuda - imported by numba.core.config (delayed, conditional, optional), numba.cuda.cudadrv.driver (conditional)
missing module named cubinlinker - imported by numba.cuda.cudadrv.driver (conditional, optional)
missing module named ptxcompiler - imported by numba.cuda.cudadrv.driver (conditional, optional)
missing module named numba.uint8 - imported by numba (top-level), numba.np.random.random_methods (top-level)
missing module named numba.uint16 - imported by numba (top-level), numba.np.random.random_methods (top-level)
missing module named numba.uint32 - imported by numba (top-level), numba.np.random.random_methods (top-level)
missing module named numba.uint64 - imported by numba (top-level), numba.np.random.random_methods (top-level)
missing module named numba.int64 - imported by numba (top-level), numba.np.random.distributions (top-level)
missing module named numba.float32 - imported by numba (top-level), numba.np.random.generator_core (top-level), numba.np.random.distributions (top-level)
missing module named numba.types.uint64 - imported by numba.types (top-level), numba.cuda.mathimpl (top-level)
missing module named numba.types.int64 - imported by numba.types (top-level), numba.cuda.mathimpl (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.float64 - imported by numba.types (top-level), numba.cuda.mathimpl (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.float32 - imported by numba.types (top-level), numba.cuda.mathimpl (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.Tuple - imported by numba.types (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.void - imported by numba.types (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.int32 - imported by numba.types (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.int16 - imported by numba.types (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named 'matplotlib.pyplot' - imported by pandas.io.formats.style (optional), pandas.plotting._matplotlib.style (delayed), pandas.plotting._matplotlib.tools (delayed), pandas.plotting._matplotlib.misc (delayed), pandas.plotting._matplotlib.core (delayed), pandas.plotting._matplotlib.boxplot (delayed), pandas.plotting._matplotlib.hist (delayed), pandas.plotting._matplotlib (delayed), tqdm.gui (delayed), pandas._testing._io (delayed), pandas._testing.asserters (delayed)
missing module named 'matplotlib.lines' - imported by pandas.plotting._matplotlib.misc (top-level), pandas.plotting._matplotlib.tools (conditional), pandas.plotting._matplotlib.boxplot (conditional)
missing module named 'matplotlib.axis' - imported by pandas.plotting._matplotlib.tools (conditional), pandas.plotting._matplotlib.core (conditional)
missing module named 'matplotlib.axes' - imported by pandas.plotting._core (conditional), pandas.plotting._misc (conditional), pandas.plotting._matplotlib.tools (conditional), pandas.plotting._matplotlib.misc (conditional), pandas.plotting._matplotlib.timeseries (conditional), pandas.plotting._matplotlib.core (delayed, conditional), pandas.plotting._matplotlib.boxplot (conditional), pandas.plotting._matplotlib.hist (conditional)
missing module named 'matplotlib.ticker' - imported by pandas.plotting._matplotlib.converter (top-level), pandas.plotting._matplotlib.tools (top-level), pandas.plotting._matplotlib.core (delayed)
missing module named 'matplotlib.table' - imported by pandas.plotting._matplotlib.tools (top-level)
missing module named 'scipy.stats' - imported by pandas.core.nanops (delayed, conditional), pandas.plotting._matplotlib.misc (delayed, conditional), pandas.plotting._matplotlib.hist (delayed)
missing module named 'matplotlib.colors' - imported by pandas.plotting._matplotlib.style (top-level), pandas.plotting._matplotlib.core (delayed)
missing module named 'matplotlib.cm' - imported by pandas.plotting._matplotlib.style (top-level)
missing module named 'matplotlib.patches' - imported by pandas.plotting._matplotlib.misc (top-level)
missing module named 'matplotlib.artist' - imported by pandas.plotting._matplotlib.boxplot (top-level), pandas.plotting._matplotlib.core (top-level)
missing module named 'matplotlib.units' - imported by pandas.plotting._matplotlib.converter (top-level)
missing module named 'matplotlib.transforms' - imported by pandas.plotting._matplotlib.converter (top-level)
missing module named 'matplotlib.dates' - imported by pandas.plotting._matplotlib.converter (top-level)
missing module named pandas.core.window._Rolling_and_Expanding - imported by pandas.core.window (delayed, optional), tqdm.std (delayed, optional)
missing module named pandas.Panel - imported by pandas (delayed, optional), tqdm.std (delayed, optional)
missing module named 'pyarrow._gcsfs' - imported by pyarrow.fs (optional)
missing module named 'setuptools_scm.git' - imported by pyarrow (delayed, optional)
missing module named setuptools_scm - imported by pyarrow (optional), tqdm.version (optional)
missing module named 'PyQt5.QtDataVisualization' - imported by qtpy.QtDataVisualization (conditional, optional)
missing module named Foundation - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named AppKit - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named PyQt4 - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named 'sqlalchemy.engine' - imported by pandas.io.sql (delayed)
missing module named 'sqlalchemy.types' - imported by pandas.io.sql (delayed, conditional)
missing module named 'sqlalchemy.schema' - imported by pandas.io.sql (delayed)
excluded module named sqlalchemy - imported by pandas.io.sql (delayed, conditional)
missing module named xlwt - imported by pandas.io.excel._xlwt (delayed, conditional)
missing module named xlsxwriter - imported by pandas.io.excel._xlsxwriter (delayed, conditional)
missing module named 'odf.config' - imported by pandas.io.excel._odswriter (delayed)
missing module named 'odf.style' - imported by pandas.io.excel._odswriter (delayed)
missing module named 'odf.text' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.table' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.opendocument' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed, conditional)
missing module named pyxlsb - imported by pandas.io.excel._pyxlsb (delayed)
missing module named 'odf.element' - imported by pandas.io.excel._odfreader (delayed)
missing module named 'odf.namespaces' - imported by pandas.io.excel._odfreader (delayed)
missing module named odf - imported by pandas.io.excel._odfreader (delayed)
excluded module named tables - imported by pandas.io.pytables (delayed, conditional)
excluded module named scipy - imported by pandas.core.missing (delayed)
missing module named pandas.UInt64Index - imported by pandas (conditional), pandas.core.dtypes.generic (conditional)
missing module named pandas.Int64Index - imported by pandas (conditional), pandas.core.dtypes.generic (conditional)
missing module named 'botocore.exceptions' - imported by pandas.io.common (delayed, conditional, optional)
missing module named pandas.ExtensionArray - imported by pandas (conditional), pandas.core.construction (conditional)
missing module named UserDict - imported by pytz.lazy (optional)
missing module named pandas.Float64Index - imported by pandas (conditional), pandas.core.dtypes.generic (conditional), pandas.core.internals.blocks (conditional), pandas.core.internals.array_manager (conditional), pandas.core.indexes.datetimes (conditional)
missing module named 'lxml.html' - imported by pandas.io.html (delayed)
missing module named bs4.builder.HTMLParserTreeBuilder - imported by bs4.builder (top-level), bs4 (top-level)
excluded module named lxml - imported by bs4.builder._lxml (top-level)
missing module named 'html5lib.treebuilders' - imported by bs4.builder._html5lib (optional)
missing module named 'html5lib.constants' - imported by bs4.builder._html5lib (top-level)
missing module named html5lib - imported by bs4.builder._html5lib (top-level)
