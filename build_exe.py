#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyInstaller打包脚本
用于将建筑艺术学院签到检查系统打包成exe文件
"""

import os
import sys
import shutil

def create_spec_file():
    """创建PyInstaller的spec文件"""
    spec_content = '''
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['attendance_checker.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('logo.jpg', '.'),  # 包含logo文件
    ],
    hiddenimports=[
        'pandas',
        'PyQt5',
        'openpyxl',
        'xlrd',
        'PyQt5.QtCore',
        'PyQt5.QtGui',
        'PyQt5.QtWidgets',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='建筑艺术学院签到检查系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='logo.jpg',  # 使用logo作为图标
)
'''
    
    with open('attendance_checker.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 已创建 attendance_checker.spec 文件")

def build_exe():
    """执行打包"""
    print("🔨 开始打包程序...")
    
    # 清理之前的构建文件
    if os.path.exists('build'):
        shutil.rmtree('build')
        print("🧹 已清理build目录")
    
    if os.path.exists('dist'):
        shutil.rmtree('dist')
        print("🧹 已清理dist目录")
    
    # 执行打包命令
    os.system('pyinstaller attendance_checker.spec')
    
    print("✅ 打包完成！")
    print("📁 可执行文件位置：dist/建筑艺术学院签到检查系统.exe")

def create_installer_script():
    """创建安装脚本"""
    installer_content = '''@echo off
echo ========================================
echo 建筑艺术学院签到检查系统 - 安装程序
echo ========================================
echo.

echo 正在复制程序文件...
if not exist "建筑艺术学院签到检查系统" mkdir "建筑艺术学院签到检查系统"

copy "建筑艺术学院签到检查系统.exe" "建筑艺术学院签到检查系统\\"
copy "logo.jpg" "建筑艺术学院签到检查系统\\" 2>nul

echo.
echo 正在创建桌面快捷方式...
echo Set oWS = WScript.CreateObject("WScript.Shell") > CreateShortcut.vbs
echo sLinkFile = "%USERPROFILE%\\Desktop\\建筑艺术学院签到检查系统.lnk" >> CreateShortcut.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> CreateShortcut.vbs
echo oLink.TargetPath = "%CD%\\建筑艺术学院签到检查系统\\建筑艺术学院签到检查系统.exe" >> CreateShortcut.vbs
echo oLink.WorkingDirectory = "%CD%\\建筑艺术学院签到检查系统" >> CreateShortcut.vbs
echo oLink.Description = "建筑艺术学院签到检查系统" >> CreateShortcut.vbs
echo oLink.Save >> CreateShortcut.vbs

cscript CreateShortcut.vbs >nul 2>&1
del CreateShortcut.vbs >nul 2>&1

echo.
echo ✅ 安装完成！
echo.
echo 程序已安装到：%CD%\\建筑艺术学院签到检查系统
echo 桌面快捷方式已创建
echo.
echo 现在可以通过桌面快捷方式或直接运行程序
echo.
pause
'''
    
    with open('dist/安装程序.bat', 'w', encoding='gbk') as f:
        f.write(installer_content)
    
    print("✅ 已创建安装脚本：dist/安装程序.bat")

def main():
    """主函数"""
    print("=" * 50)
    print("建筑艺术学院签到检查系统 - 打包工具")
    print("=" * 50)
    
    # 检查必要文件
    if not os.path.exists('attendance_checker.py'):
        print("❌ 错误：找不到 attendance_checker.py 文件")
        return
    
    if not os.path.exists('logo.jpg'):
        print("⚠️  警告：找不到 logo.jpg 文件，将使用默认图标")
    
    # 创建spec文件
    create_spec_file()
    
    # 执行打包
    build_exe()
    
    # 创建安装脚本
    if os.path.exists('dist'):
        create_installer_script()
        
        # 复制logo到dist目录
        if os.path.exists('logo.jpg'):
            shutil.copy2('logo.jpg', 'dist/')
            print("✅ 已复制logo.jpg到dist目录")
    
    print("\n" + "=" * 50)
    print("🎉 打包完成！")
    print("📁 输出目录：dist/")
    print("🚀 可执行文件：dist/建筑艺术学院签到检查系统.exe")
    print("📦 安装脚本：dist/安装程序.bat")
    print("=" * 50)

if __name__ == "__main__":
    main()
