
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 排除不必要的大型包
excludes = [
    'torch', 'torchvision', 'tensorflow', 'sklearn', 'scipy', 'matplotlib',
    'numpy.random._pickle', 'numpy.random._bounded_integers',
    'IPython', 'jupyter', 'notebook', 'sphinx', 'babel', 'jinja2',
    'PIL.ImageQt', 'PIL.ImageTk', 'tkinter', 'turtle',
    'docutils', 'pygments', 'zmq', 'cryptography', 'bcrypt',
    'distributed', 'dask', 'h5py', 'lxml', 'boto3', 'botocore',
    'transformers', 'regex', 'sacremoses', 'onnxruntime',
    'bokeh', 'plotly', 'altair', 'panel', 'param', 'holoviews',
    'skimage', 'astropy', 'imageio', 'tifffile', 'paramiko',
    'keyring', 'ruamel', 'tables', 'sqlalchemy', 'psycopg2',
    'MySQLdb', 'win32com', 'pythoncom', 'pywintypes'
]

a = Analysis(
    ['attendance_checker.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('logo.jpg', '.'),  # 包含logo文件
    ],
    hiddenimports=[
        'pandas._libs.tslibs.timedeltas',
        'pandas._libs.tslibs.np_datetime',
        'pandas._libs.tslibs.nattype',
        'pandas._libs.properties',
        'openpyxl.cell._writer',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# 手动移除不需要的模块
a.binaries = [x for x in a.binaries if not any(exclude in x[0].lower() for exclude in [
    'torch', 'sklearn', 'scipy', 'matplotlib', 'tensorflow', 'mkl', 'intel',
    'cuda', 'cudnn', 'cublas', 'cufft', 'curand', 'cusparse', 'cusolver'
])]

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='建筑艺术学院签到检查系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='logo.jpg',  # 使用logo作为图标
)
