#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
艺术学院学生签到检查系统
功能：从全院学生名单中筛选出未签到的学生
"""

import sys
import os
import pandas as pd
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                             QWidget, QPushButton, QLabel, QTextEdit, QFileDialog, 
                             QMessageBox, QProgressBar, QFrame, QGridLayout)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QPalette, QColor, QIcon, QPixmap
from datetime import datetime


class AttendanceProcessor(QThread):
    """处理签到数据的后台线程"""
    progress_updated = pyqtSignal(int)
    message_updated = pyqtSignal(str)
    finished_processing = pyqtSignal(dict)
    
    def __init__(self, all_students_file, attendance_file):
        super().__init__()
        self.all_students_file = all_students_file
        self.attendance_file = attendance_file
        
    def run(self):
        try:
            self.message_updated.emit("正在读取全院学生名单...")
            self.progress_updated.emit(20)
            
            # 读取全院学生名单
            all_students_df = pd.read_excel(self.all_students_file)
            
            self.message_updated.emit("正在读取签到名单...")
            self.progress_updated.emit(40)
            
            # 读取签到名单
            attendance_df = pd.read_excel(self.attendance_file)
            
            self.message_updated.emit("正在分析数据...")
            self.progress_updated.emit(60)
            
            # 数据处理逻辑
            result = self.process_attendance_data(all_students_df, attendance_df)
            
            self.progress_updated.emit(80)
            self.message_updated.emit("正在生成未签到学生名单...")
            
            # 保存未签到学生名单
            self.save_absent_students(result['absent_students'])
            
            self.progress_updated.emit(100)
            self.message_updated.emit("处理完成！")
            
            self.finished_processing.emit(result)
            
        except Exception as e:
            self.message_updated.emit(f"处理过程中出现错误: {str(e)}")
    
    def process_attendance_data(self, all_students_df, attendance_df):
        """处理签到数据"""
        # 假设全院名单的列名
        expected_columns = ['序号', '教学单位', '姓名', '学号', '年级', '专业班级', '班主任姓名', '备注']
        
        # 检查列名是否匹配
        if not all(col in all_students_df.columns for col in ['姓名', '学号', '专业班级']):
            # 如果列名不匹配，尝试使用索引
            if len(all_students_df.columns) >= 6:
                all_students_df.columns = expected_columns[:len(all_students_df.columns)]
        
        # 获取签到学生的姓名或学号（根据签到表的格式调整）
        if '姓名' in attendance_df.columns:
            attended_names = set(attendance_df['姓名'].dropna().astype(str))
            # 找出未签到的学生（基于姓名匹配）
            absent_students = all_students_df[~all_students_df['姓名'].astype(str).isin(attended_names)]
        elif '学号' in attendance_df.columns:
            attended_ids = set(attendance_df['学号'].dropna().astype(str))
            # 找出未签到的学生（基于学号匹配）
            absent_students = all_students_df[~all_students_df['学号'].astype(str).isin(attended_ids)]
        else:
            # 如果列名不标准，使用第一列作为匹配依据
            attended_list = set(attendance_df.iloc[:, 0].dropna().astype(str))
            absent_students = all_students_df[~all_students_df['姓名'].astype(str).isin(attended_list)]
        
        # 统计各班级未签到人数
        class_stats = absent_students.groupby('专业班级').size().to_dict()
        
        total_students = len(all_students_df)
        attended_count = len(all_students_df) - len(absent_students)
        absent_count = len(absent_students)
        
        return {
            'total_students': total_students,
            'attended_count': attended_count,
            'absent_count': absent_count,
            'absent_students': absent_students,
            'class_stats': class_stats
        }
    
    def save_absent_students(self, absent_students_df):
        """保存未签到学生名单到Excel文件"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"未签到学生名单_{timestamp}.xlsx"
        
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            absent_students_df.to_excel(writer, sheet_name='未签到学生', index=False)
        
        return filename


class ArtCollegeAttendanceChecker(QMainWindow):
    """艺术学院签到检查系统主界面"""
    
    def __init__(self):
        super().__init__()
        self.all_students_file = None
        self.attendance_file = None
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("建筑艺术学院学生签到检查系统")
        self.setGeometry(100, 100, 800, 600)

        # 设置窗口图标
        if os.path.exists("logo.jpg"):
            self.setWindowIcon(QIcon("logo.jpg"))
        
        # 设置艺术学院风格的样式
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f5f5f5, stop:1 #e8e8e8);
            }
            QLabel {
                color: #2c3e50;
                font-weight: bold;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5dade2, stop:1 #3498db);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2980b9, stop:1 #1f618d);
            }
            QTextEdit {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                padding: 5px;
                background-color: white;
            }
            QFrame {
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                background-color: white;
                margin: 5px;
            }
        """)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 标题区域
        title_frame = QFrame()
        title_layout = QHBoxLayout(title_frame)
        title_layout.setAlignment(Qt.AlignCenter)

        # 加载并显示logo
        logo_label = QLabel()
        if os.path.exists("logo.jpg"):
            pixmap = QPixmap("logo.jpg")
            # 调整logo大小
            scaled_pixmap = pixmap.scaled(60, 60, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            logo_label.setPixmap(scaled_pixmap)
        else:
            # 如果logo文件不存在，使用默认文本
            logo_label.setText("🎨")
            logo_label.setFont(QFont("Microsoft YaHei", 24))

        # 标题文字
        title_label = QLabel("建筑艺术学院学生签到检查系统")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Microsoft YaHei", 18, QFont.Bold))
        title_label.setStyleSheet("color: #8e44ad; margin: 20px; font-size: 24px;")

        # 将logo和标题添加到布局
        title_layout.addWidget(logo_label)
        title_layout.addWidget(title_label)

        main_layout.addWidget(title_frame)
        
        # 文件上传区域
        upload_frame = QFrame()
        upload_layout = QGridLayout(upload_frame)
        
        # 全院名单上传
        self.all_students_label = QLabel("📋 全院学生名单：未选择文件")
        self.all_students_btn = QPushButton("选择全院名单文件")
        self.all_students_btn.clicked.connect(self.select_all_students_file)
        
        # 签到名单上传
        self.attendance_label = QLabel("✅ 签到名单：未选择文件")
        self.attendance_btn = QPushButton("选择签到名单文件")
        self.attendance_btn.clicked.connect(self.select_attendance_file)
        
        upload_layout.addWidget(QLabel("文件上传"), 0, 0, 1, 2)
        upload_layout.addWidget(self.all_students_label, 1, 0)
        upload_layout.addWidget(self.all_students_btn, 1, 1)
        upload_layout.addWidget(self.attendance_label, 2, 0)
        upload_layout.addWidget(self.attendance_btn, 2, 1)
        
        main_layout.addWidget(upload_frame)
        
        # 操作按钮
        self.process_btn = QPushButton("🔍 开始比对分析")
        self.process_btn.clicked.connect(self.start_processing)
        self.process_btn.setEnabled(False)
        self.process_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e74c3c, stop:1 #c0392b);
                font-size: 14px;
                padding: 15px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ec7063, stop:1 #e74c3c);
            }
        """)
        main_layout.addWidget(self.process_btn)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)
        
        # 信息输出框
        info_frame = QFrame()
        info_layout = QVBoxLayout(info_frame)
        
        info_title = QLabel("📊 分析结果")
        info_title.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        info_layout.addWidget(info_title)
        
        self.info_output = QTextEdit()
        self.info_output.setPlaceholderText("分析结果将在这里显示...")
        self.info_output.setMinimumHeight(200)
        info_layout.addWidget(self.info_output)
        
        main_layout.addWidget(info_frame)
        
        # 状态栏
        self.statusBar().showMessage("就绪 - 请选择文件开始分析")

    def select_all_students_file(self):
        """选择全院学生名单文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择全院学生名单",
            "",
            "Excel文件 (*.xlsx *.xls);;所有文件 (*)"
        )

        if file_path:
            self.all_students_file = file_path
            filename = os.path.basename(file_path)
            self.all_students_label.setText(f"📋 全院学生名单：{filename}")
            self.check_files_ready()
            self.statusBar().showMessage(f"已选择全院名单：{filename}")

    def select_attendance_file(self):
        """选择签到名单文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择签到名单",
            "",
            "Excel文件 (*.xlsx *.xls);;所有文件 (*)"
        )

        if file_path:
            self.attendance_file = file_path
            filename = os.path.basename(file_path)
            self.attendance_label.setText(f"✅ 签到名单：{filename}")
            self.check_files_ready()
            self.statusBar().showMessage(f"已选择签到名单：{filename}")

    def check_files_ready(self):
        """检查文件是否都已选择"""
        if self.all_students_file and self.attendance_file:
            self.process_btn.setEnabled(True)
            self.statusBar().showMessage("文件已就绪，可以开始分析")
        else:
            self.process_btn.setEnabled(False)

    def start_processing(self):
        """开始处理数据"""
        if not self.all_students_file or not self.attendance_file:
            QMessageBox.warning(self, "警告", "请先选择全院名单和签到名单文件！")
            return

        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        # 禁用按钮
        self.process_btn.setEnabled(False)
        self.all_students_btn.setEnabled(False)
        self.attendance_btn.setEnabled(False)

        # 清空输出框
        self.info_output.clear()
        self.info_output.append("🔄 开始处理数据...")

        # 创建并启动处理线程
        self.processor = AttendanceProcessor(self.all_students_file, self.attendance_file)
        self.processor.progress_updated.connect(self.update_progress)
        self.processor.message_updated.connect(self.update_message)
        self.processor.finished_processing.connect(self.processing_finished)
        self.processor.start()

    def update_progress(self, value):
        """更新进度条"""
        self.progress_bar.setValue(value)

    def update_message(self, message):
        """更新状态消息"""
        self.statusBar().showMessage(message)
        self.info_output.append(f"• {message}")

    def processing_finished(self, result):
        """处理完成"""
        # 隐藏进度条
        self.progress_bar.setVisible(False)

        # 重新启用按钮
        self.process_btn.setEnabled(True)
        self.all_students_btn.setEnabled(True)
        self.attendance_btn.setEnabled(True)

        # 显示结果
        self.display_results(result)

        # 显示完成消息
        QMessageBox.information(
            self,
            "处理完成",
            f"分析完成！\n\n"
            f"总学生数：{result['total_students']}\n"
            f"已签到：{result['attended_count']}\n"
            f"未签到：{result['absent_count']}\n\n"
            f"未签到学生名单已保存为Excel文件。"
        )

    def display_results(self, result):
        """显示分析结果"""
        self.info_output.clear()

        # 基本统计信息
        self.info_output.append("=" * 50)
        self.info_output.append("📊 签到统计结果")
        self.info_output.append("=" * 50)
        self.info_output.append(f"📋 全院总学生数：{result['total_students']} 人")
        self.info_output.append(f"✅ 已签到学生数：{result['attended_count']} 人")
        self.info_output.append(f"❌ 未签到学生数：{result['absent_count']} 人")

        if result['total_students'] > 0:
            attendance_rate = (result['attended_count'] / result['total_students']) * 100
            self.info_output.append(f"📈 签到率：{attendance_rate:.1f}%")

        self.info_output.append("")

        # 各班级未签到统计
        if result['class_stats']:
            self.info_output.append("📚 各班级未签到人数统计：")
            self.info_output.append("-" * 40)

            for class_name, count in sorted(result['class_stats'].items()):
                self.info_output.append(f"🏫 {class_name}：{count} 人")

        self.info_output.append("")
        self.info_output.append("💾 未签到学生详细名单已保存为Excel文件")
        self.info_output.append(f"📁 文件保存位置：{os.getcwd()}")


def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用程序图标和信息
    app.setApplicationName("建筑艺术学院签到检查系统")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("建筑艺术学院")

    # 设置应用程序图标
    if os.path.exists("logo.jpg"):
        app.setWindowIcon(QIcon("logo.jpg"))

    # 创建主窗口
    window = ArtCollegeAttendanceChecker()
    window.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
