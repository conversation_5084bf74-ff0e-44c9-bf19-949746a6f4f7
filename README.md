# 艺术学院学生签到检查系统

## 功能介绍

这是一个专为艺术学院设计的学生签到检查系统，可以从全院学生名单中筛选出未签到的学生，并生成详细的统计报告。

## 主要功能

- 🎨 **艺术学院风格界面**：美观的用户界面，符合艺术学院的审美
- 📋 **文件上传**：支持上传全院学生名单和签到名单（Excel格式）
- 🔍 **智能比对**：自动比对两个名单，找出未签到的学生
- 📊 **统计分析**：显示总体签到情况和各班级详细统计
- 💾 **结果导出**：自动生成未签到学生名单的Excel文件

## 系统要求

- Python 3.7 或更高版本
- Windows/macOS/Linux 操作系统

## 安装步骤

1. **安装Python依赖**：
   ```bash
   pip install -r requirements.txt
   ```

2. **运行程序**：
   ```bash
   python attendance_checker.py
   ```

## 使用说明

### 1. 准备数据文件

**全院学生名单格式**：
- 必须包含以下列：序号、教学单位、姓名、学号、年级、专业班级、班主任姓名、备注
- 支持 .xlsx 和 .xls 格式

**签到名单格式**：
- 可以包含学生姓名或学号
- 支持 .xlsx 和 .xls 格式

### 2. 操作步骤

1. **启动程序**：运行 `python attendance_checker.py`
2. **上传文件**：
   - 点击"选择全院名单文件"按钮，选择全院学生名单
   - 点击"选择签到名单文件"按钮，选择签到名单
3. **开始分析**：点击"🔍 开始比对分析"按钮
4. **查看结果**：在信息输出框中查看详细统计结果
5. **获取文件**：程序会自动生成"未签到学生名单_时间戳.xlsx"文件

### 3. 结果说明

程序会显示以下信息：
- 📋 全院总学生数
- ✅ 已签到学生数
- ❌ 未签到学生数
- 📈 签到率
- 📚 各班级未签到人数统计

## 输出文件

程序会在当前目录下生成：
- `未签到学生名单_YYYYMMDD_HHMMSS.xlsx`：包含所有未签到学生的详细信息

## 注意事项

1. **文件格式**：确保Excel文件格式正确，列名与要求匹配
2. **数据完整性**：检查学生姓名和学号的一致性
3. **文件权限**：确保程序有读取输入文件和写入输出文件的权限
4. **中文支持**：程序完全支持中文字符

## 故障排除

### 常见问题

1. **"无法读取文件"错误**：
   - 检查文件是否被其他程序占用
   - 确认文件格式为Excel格式

2. **"列名不匹配"错误**：
   - 检查全院名单的列名是否正确
   - 确保包含必要的列：姓名、学号、专业班级

3. **程序无法启动**：
   - 检查Python版本是否为3.7+
   - 确认所有依赖包已正确安装

### 技术支持

如遇到问题，请检查：
1. Python环境是否正确配置
2. 所有依赖包是否已安装
3. 输入文件格式是否符合要求

## 版本信息

- 版本：1.0
- 开发者：艺术学院技术支持团队
- 最后更新：2024年

---

**祝您使用愉快！** 🎨✨
