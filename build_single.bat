@echo off
echo Building single exe file...
echo.

if exist build rmdir /s /q build
if exist dist rmdir /s /q dist

echo Starting build process...
pyinstaller --onefile --windowed --name "建筑艺术学院签到比对系统" --add-data "logo.jpg;." --icon "logo.jpg" attendance_checker.py

echo.
if exist "dist\建筑艺术学院签到比对系统.exe" (
    echo Build successful!
    echo File location: dist\建筑艺术学院签到比对系统.exe
) else (
    echo Build failed!
)

pause
