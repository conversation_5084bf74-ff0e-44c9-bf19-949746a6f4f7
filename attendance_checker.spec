
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['attendance_checker.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('logo.jpg', '.'),  # 包含logo文件
    ],
    hiddenimports=[
        'pandas',
        'PyQt5',
        'openpyxl',
        'xlrd',
        'PyQt5.QtCore',
        'PyQt5.QtGui',
        'PyQt5.QtWidgets',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='建筑艺术学院签到检查系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='logo.jpg',  # 使用logo作为图标
)
