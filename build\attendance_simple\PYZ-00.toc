('E:\\TLabel\\FindAbsence\\build\\attendance_simple\\PYZ-00.pyz',
 [('OpenSSL',
   'D:\\Anaconda\\Lib\\site-packages\\OpenSSL\\__init__.py',
   'PYMODULE'),
  ('OpenSSL.SSL',
   'D:\\Anaconda\\Lib\\site-packages\\OpenSSL\\SSL.py',
   'PYMODULE'),
  ('OpenSSL._util',
   'D:\\Anaconda\\Lib\\site-packages\\OpenSSL\\_util.py',
   'PYMODULE'),
  ('OpenSSL.crypto',
   'D:\\Anaconda\\Lib\\site-packages\\OpenSSL\\crypto.py',
   'PYMODULE'),
  ('OpenSSL.version',
   'D:\\Anaconda\\Lib\\site-packages\\OpenSSL\\version.py',
   'PYMODULE'),
  ('PIL', 'D:\\Anaconda\\Lib\\site-packages\\PIL\\__init__.py', 'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FitsStubImagePlugin',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\FitsStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image', 'D:\\Anaconda\\Lib\\site-packages\\PIL\\Image.py', 'PYMODULE'),
  ('PIL.ImageChops',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PyAccess',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\PyAccess.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._util', 'D:\\Anaconda\\Lib\\site-packages\\PIL\\_util.py', 'PYMODULE'),
  ('PIL._version',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'D:\\Anaconda\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PyQt5', 'D:\\Anaconda\\Lib\\site-packages\\PyQt5\\__init__.py', 'PYMODULE'),
  ('__future__', 'D:\\Anaconda\\Lib\\__future__.py', 'PYMODULE'),
  ('_aix_support', 'D:\\Anaconda\\Lib\\_aix_support.py', 'PYMODULE'),
  ('_bootsubprocess', 'D:\\Anaconda\\Lib\\_bootsubprocess.py', 'PYMODULE'),
  ('_compat_pickle', 'D:\\Anaconda\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('_compression', 'D:\\Anaconda\\Lib\\_compression.py', 'PYMODULE'),
  ('_distutils_hack',
   'D:\\Anaconda\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'D:\\Anaconda\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_markupbase', 'D:\\Anaconda\\Lib\\_markupbase.py', 'PYMODULE'),
  ('_osx_support', 'D:\\Anaconda\\Lib\\_osx_support.py', 'PYMODULE'),
  ('_py_abc', 'D:\\Anaconda\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\Anaconda\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('_pyi_rth_utils',
   'D:\\Anaconda\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'D:\\Anaconda\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_pyio', 'D:\\Anaconda\\Lib\\_pyio.py', 'PYMODULE'),
  ('_pytest',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\__init__.py',
   'PYMODULE'),
  ('_pytest._argcomplete',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\_argcomplete.py',
   'PYMODULE'),
  ('_pytest._code',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\_code\\__init__.py',
   'PYMODULE'),
  ('_pytest._code.code',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\_code\\code.py',
   'PYMODULE'),
  ('_pytest._code.source',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\_code\\source.py',
   'PYMODULE'),
  ('_pytest._io',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\_io\\__init__.py',
   'PYMODULE'),
  ('_pytest._io.saferepr',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\_io\\saferepr.py',
   'PYMODULE'),
  ('_pytest._io.terminalwriter',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\_io\\terminalwriter.py',
   'PYMODULE'),
  ('_pytest._io.wcwidth',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\_io\\wcwidth.py',
   'PYMODULE'),
  ('_pytest._py',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\_py\\__init__.py',
   'PYMODULE'),
  ('_pytest._py.error',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\_py\\error.py',
   'PYMODULE'),
  ('_pytest._py.path',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\_py\\path.py',
   'PYMODULE'),
  ('_pytest._version',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\_version.py',
   'PYMODULE'),
  ('_pytest.assertion',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\assertion\\__init__.py',
   'PYMODULE'),
  ('_pytest.assertion.rewrite',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\assertion\\rewrite.py',
   'PYMODULE'),
  ('_pytest.assertion.truncate',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\assertion\\truncate.py',
   'PYMODULE'),
  ('_pytest.assertion.util',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\assertion\\util.py',
   'PYMODULE'),
  ('_pytest.cacheprovider',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\cacheprovider.py',
   'PYMODULE'),
  ('_pytest.capture',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\capture.py',
   'PYMODULE'),
  ('_pytest.compat',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\compat.py',
   'PYMODULE'),
  ('_pytest.config',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\config\\__init__.py',
   'PYMODULE'),
  ('_pytest.config.argparsing',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\config\\argparsing.py',
   'PYMODULE'),
  ('_pytest.config.compat',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\config\\compat.py',
   'PYMODULE'),
  ('_pytest.config.exceptions',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\config\\exceptions.py',
   'PYMODULE'),
  ('_pytest.config.findpaths',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\config\\findpaths.py',
   'PYMODULE'),
  ('_pytest.debugging',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\debugging.py',
   'PYMODULE'),
  ('_pytest.deprecated',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\deprecated.py',
   'PYMODULE'),
  ('_pytest.doctest',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\doctest.py',
   'PYMODULE'),
  ('_pytest.faulthandler',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\faulthandler.py',
   'PYMODULE'),
  ('_pytest.fixtures',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\fixtures.py',
   'PYMODULE'),
  ('_pytest.freeze_support',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\freeze_support.py',
   'PYMODULE'),
  ('_pytest.helpconfig',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\helpconfig.py',
   'PYMODULE'),
  ('_pytest.hookspec',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\hookspec.py',
   'PYMODULE'),
  ('_pytest.junitxml',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\junitxml.py',
   'PYMODULE'),
  ('_pytest.legacypath',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\legacypath.py',
   'PYMODULE'),
  ('_pytest.logging',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\logging.py',
   'PYMODULE'),
  ('_pytest.main',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\main.py',
   'PYMODULE'),
  ('_pytest.mark',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\mark\\__init__.py',
   'PYMODULE'),
  ('_pytest.mark.expression',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\mark\\expression.py',
   'PYMODULE'),
  ('_pytest.mark.structures',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\mark\\structures.py',
   'PYMODULE'),
  ('_pytest.monkeypatch',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\monkeypatch.py',
   'PYMODULE'),
  ('_pytest.nodes',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\nodes.py',
   'PYMODULE'),
  ('_pytest.nose',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\nose.py',
   'PYMODULE'),
  ('_pytest.outcomes',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\outcomes.py',
   'PYMODULE'),
  ('_pytest.pastebin',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\pastebin.py',
   'PYMODULE'),
  ('_pytest.pathlib',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\pathlib.py',
   'PYMODULE'),
  ('_pytest.pytester',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\pytester.py',
   'PYMODULE'),
  ('_pytest.pytester_assertions',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\pytester_assertions.py',
   'PYMODULE'),
  ('_pytest.python',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\python.py',
   'PYMODULE'),
  ('_pytest.python_api',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\python_api.py',
   'PYMODULE'),
  ('_pytest.python_path',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\python_path.py',
   'PYMODULE'),
  ('_pytest.recwarn',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\recwarn.py',
   'PYMODULE'),
  ('_pytest.reports',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\reports.py',
   'PYMODULE'),
  ('_pytest.runner',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\runner.py',
   'PYMODULE'),
  ('_pytest.scope',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\scope.py',
   'PYMODULE'),
  ('_pytest.setuponly',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\setuponly.py',
   'PYMODULE'),
  ('_pytest.setupplan',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\setupplan.py',
   'PYMODULE'),
  ('_pytest.skipping',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\skipping.py',
   'PYMODULE'),
  ('_pytest.stash',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\stash.py',
   'PYMODULE'),
  ('_pytest.stepwise',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\stepwise.py',
   'PYMODULE'),
  ('_pytest.terminal',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\terminal.py',
   'PYMODULE'),
  ('_pytest.threadexception',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\threadexception.py',
   'PYMODULE'),
  ('_pytest.timing',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\timing.py',
   'PYMODULE'),
  ('_pytest.tmpdir',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\tmpdir.py',
   'PYMODULE'),
  ('_pytest.unittest',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\unittest.py',
   'PYMODULE'),
  ('_pytest.unraisableexception',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\unraisableexception.py',
   'PYMODULE'),
  ('_pytest.warning_types',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\warning_types.py',
   'PYMODULE'),
  ('_pytest.warnings',
   'D:\\Anaconda\\Lib\\site-packages\\_pytest\\warnings.py',
   'PYMODULE'),
  ('_sitebuiltins', 'D:\\Anaconda\\Lib\\_sitebuiltins.py', 'PYMODULE'),
  ('_strptime', 'D:\\Anaconda\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local', 'D:\\Anaconda\\Lib\\_threading_local.py', 'PYMODULE'),
  ('aiohttp',
   'D:\\Anaconda\\Lib\\site-packages\\aiohttp\\__init__.py',
   'PYMODULE'),
  ('aiohttp.abc',
   'D:\\Anaconda\\Lib\\site-packages\\aiohttp\\abc.py',
   'PYMODULE'),
  ('aiohttp.base_protocol',
   'D:\\Anaconda\\Lib\\site-packages\\aiohttp\\base_protocol.py',
   'PYMODULE'),
  ('aiohttp.client',
   'D:\\Anaconda\\Lib\\site-packages\\aiohttp\\client.py',
   'PYMODULE'),
  ('aiohttp.client_exceptions',
   'D:\\Anaconda\\Lib\\site-packages\\aiohttp\\client_exceptions.py',
   'PYMODULE'),
  ('aiohttp.client_proto',
   'D:\\Anaconda\\Lib\\site-packages\\aiohttp\\client_proto.py',
   'PYMODULE'),
  ('aiohttp.client_reqrep',
   'D:\\Anaconda\\Lib\\site-packages\\aiohttp\\client_reqrep.py',
   'PYMODULE'),
  ('aiohttp.client_ws',
   'D:\\Anaconda\\Lib\\site-packages\\aiohttp\\client_ws.py',
   'PYMODULE'),
  ('aiohttp.connector',
   'D:\\Anaconda\\Lib\\site-packages\\aiohttp\\connector.py',
   'PYMODULE'),
  ('aiohttp.cookiejar',
   'D:\\Anaconda\\Lib\\site-packages\\aiohttp\\cookiejar.py',
   'PYMODULE'),
  ('aiohttp.formdata',
   'D:\\Anaconda\\Lib\\site-packages\\aiohttp\\formdata.py',
   'PYMODULE'),
  ('aiohttp.hdrs',
   'D:\\Anaconda\\Lib\\site-packages\\aiohttp\\hdrs.py',
   'PYMODULE'),
  ('aiohttp.helpers',
   'D:\\Anaconda\\Lib\\site-packages\\aiohttp\\helpers.py',
   'PYMODULE'),
  ('aiohttp.http',
   'D:\\Anaconda\\Lib\\site-packages\\aiohttp\\http.py',
   'PYMODULE'),
  ('aiohttp.http_exceptions',
   'D:\\Anaconda\\Lib\\site-packages\\aiohttp\\http_exceptions.py',
   'PYMODULE'),
  ('aiohttp.http_parser',
   'D:\\Anaconda\\Lib\\site-packages\\aiohttp\\http_parser.py',
   'PYMODULE'),
  ('aiohttp.http_websocket',
   'D:\\Anaconda\\Lib\\site-packages\\aiohttp\\http_websocket.py',
   'PYMODULE'),
  ('aiohttp.http_writer',
   'D:\\Anaconda\\Lib\\site-packages\\aiohttp\\http_writer.py',
   'PYMODULE'),
  ('aiohttp.locks',
   'D:\\Anaconda\\Lib\\site-packages\\aiohttp\\locks.py',
   'PYMODULE'),
  ('aiohttp.log',
   'D:\\Anaconda\\Lib\\site-packages\\aiohttp\\log.py',
   'PYMODULE'),
  ('aiohttp.multipart',
   'D:\\Anaconda\\Lib\\site-packages\\aiohttp\\multipart.py',
   'PYMODULE'),
  ('aiohttp.payload',
   'D:\\Anaconda\\Lib\\site-packages\\aiohttp\\payload.py',
   'PYMODULE'),
  ('aiohttp.payload_streamer',
   'D:\\Anaconda\\Lib\\site-packages\\aiohttp\\payload_streamer.py',
   'PYMODULE'),
  ('aiohttp.resolver',
   'D:\\Anaconda\\Lib\\site-packages\\aiohttp\\resolver.py',
   'PYMODULE'),
  ('aiohttp.streams',
   'D:\\Anaconda\\Lib\\site-packages\\aiohttp\\streams.py',
   'PYMODULE'),
  ('aiohttp.tcp_helpers',
   'D:\\Anaconda\\Lib\\site-packages\\aiohttp\\tcp_helpers.py',
   'PYMODULE'),
  ('aiohttp.tracing',
   'D:\\Anaconda\\Lib\\site-packages\\aiohttp\\tracing.py',
   'PYMODULE'),
  ('aiohttp.typedefs',
   'D:\\Anaconda\\Lib\\site-packages\\aiohttp\\typedefs.py',
   'PYMODULE'),
  ('aiohttp.web',
   'D:\\Anaconda\\Lib\\site-packages\\aiohttp\\web.py',
   'PYMODULE'),
  ('aiohttp.web_app',
   'D:\\Anaconda\\Lib\\site-packages\\aiohttp\\web_app.py',
   'PYMODULE'),
  ('aiohttp.web_exceptions',
   'D:\\Anaconda\\Lib\\site-packages\\aiohttp\\web_exceptions.py',
   'PYMODULE'),
  ('aiohttp.web_fileresponse',
   'D:\\Anaconda\\Lib\\site-packages\\aiohttp\\web_fileresponse.py',
   'PYMODULE'),
  ('aiohttp.web_log',
   'D:\\Anaconda\\Lib\\site-packages\\aiohttp\\web_log.py',
   'PYMODULE'),
  ('aiohttp.web_middlewares',
   'D:\\Anaconda\\Lib\\site-packages\\aiohttp\\web_middlewares.py',
   'PYMODULE'),
  ('aiohttp.web_protocol',
   'D:\\Anaconda\\Lib\\site-packages\\aiohttp\\web_protocol.py',
   'PYMODULE'),
  ('aiohttp.web_request',
   'D:\\Anaconda\\Lib\\site-packages\\aiohttp\\web_request.py',
   'PYMODULE'),
  ('aiohttp.web_response',
   'D:\\Anaconda\\Lib\\site-packages\\aiohttp\\web_response.py',
   'PYMODULE'),
  ('aiohttp.web_routedef',
   'D:\\Anaconda\\Lib\\site-packages\\aiohttp\\web_routedef.py',
   'PYMODULE'),
  ('aiohttp.web_runner',
   'D:\\Anaconda\\Lib\\site-packages\\aiohttp\\web_runner.py',
   'PYMODULE'),
  ('aiohttp.web_server',
   'D:\\Anaconda\\Lib\\site-packages\\aiohttp\\web_server.py',
   'PYMODULE'),
  ('aiohttp.web_urldispatcher',
   'D:\\Anaconda\\Lib\\site-packages\\aiohttp\\web_urldispatcher.py',
   'PYMODULE'),
  ('aiohttp.web_ws',
   'D:\\Anaconda\\Lib\\site-packages\\aiohttp\\web_ws.py',
   'PYMODULE'),
  ('aiohttp.worker',
   'D:\\Anaconda\\Lib\\site-packages\\aiohttp\\worker.py',
   'PYMODULE'),
  ('aiosignal',
   'D:\\Anaconda\\Lib\\site-packages\\aiosignal\\__init__.py',
   'PYMODULE'),
  ('argparse', 'D:\\Anaconda\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'D:\\Anaconda\\Lib\\ast.py', 'PYMODULE'),
  ('async_timeout',
   'D:\\Anaconda\\Lib\\site-packages\\async_timeout\\__init__.py',
   'PYMODULE'),
  ('asyncio', 'D:\\Anaconda\\Lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.base_events',
   'D:\\Anaconda\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\Anaconda\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\Anaconda\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\Anaconda\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants', 'D:\\Anaconda\\Lib\\asyncio\\constants.py', 'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\Anaconda\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events', 'D:\\Anaconda\\Lib\\asyncio\\events.py', 'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\Anaconda\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\Anaconda\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures', 'D:\\Anaconda\\Lib\\asyncio\\futures.py', 'PYMODULE'),
  ('asyncio.locks', 'D:\\Anaconda\\Lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.log', 'D:\\Anaconda\\Lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.mixins', 'D:\\Anaconda\\Lib\\asyncio\\mixins.py', 'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\Anaconda\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols', 'D:\\Anaconda\\Lib\\asyncio\\protocols.py', 'PYMODULE'),
  ('asyncio.queues', 'D:\\Anaconda\\Lib\\asyncio\\queues.py', 'PYMODULE'),
  ('asyncio.runners', 'D:\\Anaconda\\Lib\\asyncio\\runners.py', 'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\Anaconda\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto', 'D:\\Anaconda\\Lib\\asyncio\\sslproto.py', 'PYMODULE'),
  ('asyncio.staggered', 'D:\\Anaconda\\Lib\\asyncio\\staggered.py', 'PYMODULE'),
  ('asyncio.streams', 'D:\\Anaconda\\Lib\\asyncio\\streams.py', 'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\Anaconda\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'D:\\Anaconda\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks', 'D:\\Anaconda\\Lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.threads', 'D:\\Anaconda\\Lib\\asyncio\\threads.py', 'PYMODULE'),
  ('asyncio.timeouts', 'D:\\Anaconda\\Lib\\asyncio\\timeouts.py', 'PYMODULE'),
  ('asyncio.transports',
   'D:\\Anaconda\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock', 'D:\\Anaconda\\Lib\\asyncio\\trsock.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\Anaconda\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\Anaconda\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\Anaconda\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('attr', 'D:\\Anaconda\\Lib\\site-packages\\attr\\__init__.py', 'PYMODULE'),
  ('attr._cmp', 'D:\\Anaconda\\Lib\\site-packages\\attr\\_cmp.py', 'PYMODULE'),
  ('attr._compat',
   'D:\\Anaconda\\Lib\\site-packages\\attr\\_compat.py',
   'PYMODULE'),
  ('attr._config',
   'D:\\Anaconda\\Lib\\site-packages\\attr\\_config.py',
   'PYMODULE'),
  ('attr._funcs',
   'D:\\Anaconda\\Lib\\site-packages\\attr\\_funcs.py',
   'PYMODULE'),
  ('attr._make',
   'D:\\Anaconda\\Lib\\site-packages\\attr\\_make.py',
   'PYMODULE'),
  ('attr._next_gen',
   'D:\\Anaconda\\Lib\\site-packages\\attr\\_next_gen.py',
   'PYMODULE'),
  ('attr._version_info',
   'D:\\Anaconda\\Lib\\site-packages\\attr\\_version_info.py',
   'PYMODULE'),
  ('attr.converters',
   'D:\\Anaconda\\Lib\\site-packages\\attr\\converters.py',
   'PYMODULE'),
  ('attr.exceptions',
   'D:\\Anaconda\\Lib\\site-packages\\attr\\exceptions.py',
   'PYMODULE'),
  ('attr.filters',
   'D:\\Anaconda\\Lib\\site-packages\\attr\\filters.py',
   'PYMODULE'),
  ('attr.setters',
   'D:\\Anaconda\\Lib\\site-packages\\attr\\setters.py',
   'PYMODULE'),
  ('attr.validators',
   'D:\\Anaconda\\Lib\\site-packages\\attr\\validators.py',
   'PYMODULE'),
  ('attrs', 'D:\\Anaconda\\Lib\\site-packages\\attrs\\__init__.py', 'PYMODULE'),
  ('attrs.converters',
   'D:\\Anaconda\\Lib\\site-packages\\attrs\\converters.py',
   'PYMODULE'),
  ('attrs.exceptions',
   'D:\\Anaconda\\Lib\\site-packages\\attrs\\exceptions.py',
   'PYMODULE'),
  ('attrs.filters',
   'D:\\Anaconda\\Lib\\site-packages\\attrs\\filters.py',
   'PYMODULE'),
  ('attrs.setters',
   'D:\\Anaconda\\Lib\\site-packages\\attrs\\setters.py',
   'PYMODULE'),
  ('attrs.validators',
   'D:\\Anaconda\\Lib\\site-packages\\attrs\\validators.py',
   'PYMODULE'),
  ('base64', 'D:\\Anaconda\\Lib\\base64.py', 'PYMODULE'),
  ('bdb', 'D:\\Anaconda\\Lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'D:\\Anaconda\\Lib\\bisect.py', 'PYMODULE'),
  ('brotli',
   'D:\\Anaconda\\Lib\\site-packages\\brotli\\__init__.py',
   'PYMODULE'),
  ('brotli.brotli',
   'D:\\Anaconda\\Lib\\site-packages\\brotli\\brotli.py',
   'PYMODULE'),
  ('bs4', 'D:\\Anaconda\\Lib\\site-packages\\bs4\\__init__.py', 'PYMODULE'),
  ('bs4.builder',
   'D:\\Anaconda\\Lib\\site-packages\\bs4\\builder\\__init__.py',
   'PYMODULE'),
  ('bs4.builder._html5lib',
   'D:\\Anaconda\\Lib\\site-packages\\bs4\\builder\\_html5lib.py',
   'PYMODULE'),
  ('bs4.builder._htmlparser',
   'D:\\Anaconda\\Lib\\site-packages\\bs4\\builder\\_htmlparser.py',
   'PYMODULE'),
  ('bs4.builder._lxml',
   'D:\\Anaconda\\Lib\\site-packages\\bs4\\builder\\_lxml.py',
   'PYMODULE'),
  ('bs4.css', 'D:\\Anaconda\\Lib\\site-packages\\bs4\\css.py', 'PYMODULE'),
  ('bs4.dammit',
   'D:\\Anaconda\\Lib\\site-packages\\bs4\\dammit.py',
   'PYMODULE'),
  ('bs4.element',
   'D:\\Anaconda\\Lib\\site-packages\\bs4\\element.py',
   'PYMODULE'),
  ('bs4.formatter',
   'D:\\Anaconda\\Lib\\site-packages\\bs4\\formatter.py',
   'PYMODULE'),
  ('bz2', 'D:\\Anaconda\\Lib\\bz2.py', 'PYMODULE'),
  ('cProfile', 'D:\\Anaconda\\Lib\\cProfile.py', 'PYMODULE'),
  ('calendar', 'D:\\Anaconda\\Lib\\calendar.py', 'PYMODULE'),
  ('certifi',
   'D:\\Anaconda\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'D:\\Anaconda\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('cffi', 'D:\\Anaconda\\Lib\\site-packages\\cffi\\__init__.py', 'PYMODULE'),
  ('cffi.api', 'D:\\Anaconda\\Lib\\site-packages\\cffi\\api.py', 'PYMODULE'),
  ('cffi.cffi_opcode',
   'D:\\Anaconda\\Lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'D:\\Anaconda\\Lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.cparser',
   'D:\\Anaconda\\Lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('cffi.error',
   'D:\\Anaconda\\Lib\\site-packages\\cffi\\error.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   'D:\\Anaconda\\Lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.lock', 'D:\\Anaconda\\Lib\\site-packages\\cffi\\lock.py', 'PYMODULE'),
  ('cffi.model',
   'D:\\Anaconda\\Lib\\site-packages\\cffi\\model.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   'D:\\Anaconda\\Lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'D:\\Anaconda\\Lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'D:\\Anaconda\\Lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'D:\\Anaconda\\Lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.verifier',
   'D:\\Anaconda\\Lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('cgi', 'D:\\Anaconda\\Lib\\cgi.py', 'PYMODULE'),
  ('chardet',
   'D:\\Anaconda\\Lib\\site-packages\\chardet\\__init__.py',
   'PYMODULE'),
  ('chardet.big5freq',
   'D:\\Anaconda\\Lib\\site-packages\\chardet\\big5freq.py',
   'PYMODULE'),
  ('chardet.big5prober',
   'D:\\Anaconda\\Lib\\site-packages\\chardet\\big5prober.py',
   'PYMODULE'),
  ('chardet.chardistribution',
   'D:\\Anaconda\\Lib\\site-packages\\chardet\\chardistribution.py',
   'PYMODULE'),
  ('chardet.charsetgroupprober',
   'D:\\Anaconda\\Lib\\site-packages\\chardet\\charsetgroupprober.py',
   'PYMODULE'),
  ('chardet.charsetprober',
   'D:\\Anaconda\\Lib\\site-packages\\chardet\\charsetprober.py',
   'PYMODULE'),
  ('chardet.codingstatemachine',
   'D:\\Anaconda\\Lib\\site-packages\\chardet\\codingstatemachine.py',
   'PYMODULE'),
  ('chardet.cp949prober',
   'D:\\Anaconda\\Lib\\site-packages\\chardet\\cp949prober.py',
   'PYMODULE'),
  ('chardet.enums',
   'D:\\Anaconda\\Lib\\site-packages\\chardet\\enums.py',
   'PYMODULE'),
  ('chardet.escprober',
   'D:\\Anaconda\\Lib\\site-packages\\chardet\\escprober.py',
   'PYMODULE'),
  ('chardet.escsm',
   'D:\\Anaconda\\Lib\\site-packages\\chardet\\escsm.py',
   'PYMODULE'),
  ('chardet.eucjpprober',
   'D:\\Anaconda\\Lib\\site-packages\\chardet\\eucjpprober.py',
   'PYMODULE'),
  ('chardet.euckrfreq',
   'D:\\Anaconda\\Lib\\site-packages\\chardet\\euckrfreq.py',
   'PYMODULE'),
  ('chardet.euckrprober',
   'D:\\Anaconda\\Lib\\site-packages\\chardet\\euckrprober.py',
   'PYMODULE'),
  ('chardet.euctwfreq',
   'D:\\Anaconda\\Lib\\site-packages\\chardet\\euctwfreq.py',
   'PYMODULE'),
  ('chardet.euctwprober',
   'D:\\Anaconda\\Lib\\site-packages\\chardet\\euctwprober.py',
   'PYMODULE'),
  ('chardet.gb2312freq',
   'D:\\Anaconda\\Lib\\site-packages\\chardet\\gb2312freq.py',
   'PYMODULE'),
  ('chardet.gb2312prober',
   'D:\\Anaconda\\Lib\\site-packages\\chardet\\gb2312prober.py',
   'PYMODULE'),
  ('chardet.hebrewprober',
   'D:\\Anaconda\\Lib\\site-packages\\chardet\\hebrewprober.py',
   'PYMODULE'),
  ('chardet.jisfreq',
   'D:\\Anaconda\\Lib\\site-packages\\chardet\\jisfreq.py',
   'PYMODULE'),
  ('chardet.jpcntx',
   'D:\\Anaconda\\Lib\\site-packages\\chardet\\jpcntx.py',
   'PYMODULE'),
  ('chardet.langbulgarianmodel',
   'D:\\Anaconda\\Lib\\site-packages\\chardet\\langbulgarianmodel.py',
   'PYMODULE'),
  ('chardet.langgreekmodel',
   'D:\\Anaconda\\Lib\\site-packages\\chardet\\langgreekmodel.py',
   'PYMODULE'),
  ('chardet.langhebrewmodel',
   'D:\\Anaconda\\Lib\\site-packages\\chardet\\langhebrewmodel.py',
   'PYMODULE'),
  ('chardet.langrussianmodel',
   'D:\\Anaconda\\Lib\\site-packages\\chardet\\langrussianmodel.py',
   'PYMODULE'),
  ('chardet.langthaimodel',
   'D:\\Anaconda\\Lib\\site-packages\\chardet\\langthaimodel.py',
   'PYMODULE'),
  ('chardet.langturkishmodel',
   'D:\\Anaconda\\Lib\\site-packages\\chardet\\langturkishmodel.py',
   'PYMODULE'),
  ('chardet.latin1prober',
   'D:\\Anaconda\\Lib\\site-packages\\chardet\\latin1prober.py',
   'PYMODULE'),
  ('chardet.mbcharsetprober',
   'D:\\Anaconda\\Lib\\site-packages\\chardet\\mbcharsetprober.py',
   'PYMODULE'),
  ('chardet.mbcsgroupprober',
   'D:\\Anaconda\\Lib\\site-packages\\chardet\\mbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.mbcssm',
   'D:\\Anaconda\\Lib\\site-packages\\chardet\\mbcssm.py',
   'PYMODULE'),
  ('chardet.sbcharsetprober',
   'D:\\Anaconda\\Lib\\site-packages\\chardet\\sbcharsetprober.py',
   'PYMODULE'),
  ('chardet.sbcsgroupprober',
   'D:\\Anaconda\\Lib\\site-packages\\chardet\\sbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.sjisprober',
   'D:\\Anaconda\\Lib\\site-packages\\chardet\\sjisprober.py',
   'PYMODULE'),
  ('chardet.universaldetector',
   'D:\\Anaconda\\Lib\\site-packages\\chardet\\universaldetector.py',
   'PYMODULE'),
  ('chardet.utf8prober',
   'D:\\Anaconda\\Lib\\site-packages\\chardet\\utf8prober.py',
   'PYMODULE'),
  ('chardet.version',
   'D:\\Anaconda\\Lib\\site-packages\\chardet\\version.py',
   'PYMODULE'),
  ('charset_normalizer',
   'D:\\Anaconda\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'D:\\Anaconda\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.assets',
   'D:\\Anaconda\\Lib\\site-packages\\charset_normalizer\\assets\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'D:\\Anaconda\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'D:\\Anaconda\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'D:\\Anaconda\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.md',
   'D:\\Anaconda\\Lib\\site-packages\\charset_normalizer\\md.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'D:\\Anaconda\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'D:\\Anaconda\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'D:\\Anaconda\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('cloudpickle',
   'D:\\Anaconda\\Lib\\site-packages\\cloudpickle\\__init__.py',
   'PYMODULE'),
  ('cloudpickle.cloudpickle',
   'D:\\Anaconda\\Lib\\site-packages\\cloudpickle\\cloudpickle.py',
   'PYMODULE'),
  ('cloudpickle.cloudpickle_fast',
   'D:\\Anaconda\\Lib\\site-packages\\cloudpickle\\cloudpickle_fast.py',
   'PYMODULE'),
  ('cloudpickle.compat',
   'D:\\Anaconda\\Lib\\site-packages\\cloudpickle\\compat.py',
   'PYMODULE'),
  ('cmd', 'D:\\Anaconda\\Lib\\cmd.py', 'PYMODULE'),
  ('code', 'D:\\Anaconda\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\Anaconda\\Lib\\codeop.py', 'PYMODULE'),
  ('colorama',
   'D:\\Anaconda\\Lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   'D:\\Anaconda\\Lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'D:\\Anaconda\\Lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   'D:\\Anaconda\\Lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'D:\\Anaconda\\Lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'D:\\Anaconda\\Lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorsys', 'D:\\Anaconda\\Lib\\colorsys.py', 'PYMODULE'),
  ('comm', 'D:\\Anaconda\\Lib\\site-packages\\comm\\__init__.py', 'PYMODULE'),
  ('comm.base_comm',
   'D:\\Anaconda\\Lib\\site-packages\\comm\\base_comm.py',
   'PYMODULE'),
  ('concurrent', 'D:\\Anaconda\\Lib\\concurrent\\__init__.py', 'PYMODULE'),
  ('concurrent.futures',
   'D:\\Anaconda\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\Anaconda\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\Anaconda\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\Anaconda\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser', 'D:\\Anaconda\\Lib\\configparser.py', 'PYMODULE'),
  ('constantly',
   'D:\\Anaconda\\Lib\\site-packages\\constantly\\__init__.py',
   'PYMODULE'),
  ('constantly._constants',
   'D:\\Anaconda\\Lib\\site-packages\\constantly\\_constants.py',
   'PYMODULE'),
  ('constantly._version',
   'D:\\Anaconda\\Lib\\site-packages\\constantly\\_version.py',
   'PYMODULE'),
  ('contextlib', 'D:\\Anaconda\\Lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'D:\\Anaconda\\Lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'D:\\Anaconda\\Lib\\copy.py', 'PYMODULE'),
  ('csv', 'D:\\Anaconda\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'D:\\Anaconda\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._aix', 'D:\\Anaconda\\Lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes._endian', 'D:\\Anaconda\\Lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('ctypes.macholib',
   'D:\\Anaconda\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\Anaconda\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\Anaconda\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\Anaconda\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util', 'D:\\Anaconda\\Lib\\ctypes\\util.py', 'PYMODULE'),
  ('ctypes.wintypes', 'D:\\Anaconda\\Lib\\ctypes\\wintypes.py', 'PYMODULE'),
  ('curses', 'D:\\Anaconda\\Lib\\curses\\__init__.py', 'PYMODULE'),
  ('curses.has_key', 'D:\\Anaconda\\Lib\\curses\\has_key.py', 'PYMODULE'),
  ('dataclasses', 'D:\\Anaconda\\Lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'D:\\Anaconda\\Lib\\datetime.py', 'PYMODULE'),
  ('dateutil',
   'D:\\Anaconda\\Lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._common',
   'D:\\Anaconda\\Lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil._version',
   'D:\\Anaconda\\Lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil.easter',
   'D:\\Anaconda\\Lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil.parser',
   'D:\\Anaconda\\Lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'D:\\Anaconda\\Lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'D:\\Anaconda\\Lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'D:\\Anaconda\\Lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'D:\\Anaconda\\Lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.tz',
   'D:\\Anaconda\\Lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'D:\\Anaconda\\Lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'D:\\Anaconda\\Lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'D:\\Anaconda\\Lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'D:\\Anaconda\\Lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'D:\\Anaconda\\Lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('dbm', 'D:\\Anaconda\\Lib\\dbm\\__init__.py', 'PYMODULE'),
  ('dbm.dumb', 'D:\\Anaconda\\Lib\\dbm\\dumb.py', 'PYMODULE'),
  ('dbm.gnu', 'D:\\Anaconda\\Lib\\dbm\\gnu.py', 'PYMODULE'),
  ('dbm.ndbm', 'D:\\Anaconda\\Lib\\dbm\\ndbm.py', 'PYMODULE'),
  ('debugpy',
   'D:\\Anaconda\\Lib\\site-packages\\debugpy\\__init__.py',
   'PYMODULE'),
  ('debugpy._vendored',
   'D:\\Anaconda\\Lib\\site-packages\\debugpy\\_vendored\\__init__.py',
   'PYMODULE'),
  ('debugpy._vendored._util',
   'D:\\Anaconda\\Lib\\site-packages\\debugpy\\_vendored\\_util.py',
   'PYMODULE'),
  ('debugpy._vendored.force_pydevd',
   'D:\\Anaconda\\Lib\\site-packages\\debugpy\\_vendored\\force_pydevd.py',
   'PYMODULE'),
  ('debugpy._version',
   'D:\\Anaconda\\Lib\\site-packages\\debugpy\\_version.py',
   'PYMODULE'),
  ('debugpy.adapter',
   'D:\\Anaconda\\Lib\\site-packages\\debugpy\\adapter\\__init__.py',
   'PYMODULE'),
  ('debugpy.common',
   'D:\\Anaconda\\Lib\\site-packages\\debugpy\\common\\__init__.py',
   'PYMODULE'),
  ('debugpy.common.compat',
   'D:\\Anaconda\\Lib\\site-packages\\debugpy\\common\\compat.py',
   'PYMODULE'),
  ('debugpy.common.fmt',
   'D:\\Anaconda\\Lib\\site-packages\\debugpy\\common\\fmt.py',
   'PYMODULE'),
  ('debugpy.common.json',
   'D:\\Anaconda\\Lib\\site-packages\\debugpy\\common\\json.py',
   'PYMODULE'),
  ('debugpy.common.log',
   'D:\\Anaconda\\Lib\\site-packages\\debugpy\\common\\log.py',
   'PYMODULE'),
  ('debugpy.common.sockets',
   'D:\\Anaconda\\Lib\\site-packages\\debugpy\\common\\sockets.py',
   'PYMODULE'),
  ('debugpy.common.timestamp',
   'D:\\Anaconda\\Lib\\site-packages\\debugpy\\common\\timestamp.py',
   'PYMODULE'),
  ('debugpy.common.util',
   'D:\\Anaconda\\Lib\\site-packages\\debugpy\\common\\util.py',
   'PYMODULE'),
  ('debugpy.server',
   'D:\\Anaconda\\Lib\\site-packages\\debugpy\\server\\__init__.py',
   'PYMODULE'),
  ('debugpy.server.api',
   'D:\\Anaconda\\Lib\\site-packages\\debugpy\\server\\api.py',
   'PYMODULE'),
  ('decimal', 'D:\\Anaconda\\Lib\\decimal.py', 'PYMODULE'),
  ('defusedxml',
   'D:\\Anaconda\\Lib\\site-packages\\defusedxml\\__init__.py',
   'PYMODULE'),
  ('defusedxml.ElementTree',
   'D:\\Anaconda\\Lib\\site-packages\\defusedxml\\ElementTree.py',
   'PYMODULE'),
  ('defusedxml.cElementTree',
   'D:\\Anaconda\\Lib\\site-packages\\defusedxml\\cElementTree.py',
   'PYMODULE'),
  ('defusedxml.common',
   'D:\\Anaconda\\Lib\\site-packages\\defusedxml\\common.py',
   'PYMODULE'),
  ('defusedxml.expatbuilder',
   'D:\\Anaconda\\Lib\\site-packages\\defusedxml\\expatbuilder.py',
   'PYMODULE'),
  ('defusedxml.expatreader',
   'D:\\Anaconda\\Lib\\site-packages\\defusedxml\\expatreader.py',
   'PYMODULE'),
  ('defusedxml.minidom',
   'D:\\Anaconda\\Lib\\site-packages\\defusedxml\\minidom.py',
   'PYMODULE'),
  ('defusedxml.pulldom',
   'D:\\Anaconda\\Lib\\site-packages\\defusedxml\\pulldom.py',
   'PYMODULE'),
  ('defusedxml.sax',
   'D:\\Anaconda\\Lib\\site-packages\\defusedxml\\sax.py',
   'PYMODULE'),
  ('defusedxml.xmlrpc',
   'D:\\Anaconda\\Lib\\site-packages\\defusedxml\\xmlrpc.py',
   'PYMODULE'),
  ('difflib', 'D:\\Anaconda\\Lib\\difflib.py', 'PYMODULE'),
  ('dill', 'D:\\Anaconda\\Lib\\site-packages\\dill\\__init__.py', 'PYMODULE'),
  ('dill.__info__',
   'D:\\Anaconda\\Lib\\site-packages\\dill\\__info__.py',
   'PYMODULE'),
  ('dill._dill',
   'D:\\Anaconda\\Lib\\site-packages\\dill\\_dill.py',
   'PYMODULE'),
  ('dill._objects',
   'D:\\Anaconda\\Lib\\site-packages\\dill\\_objects.py',
   'PYMODULE'),
  ('dill._shims',
   'D:\\Anaconda\\Lib\\site-packages\\dill\\_shims.py',
   'PYMODULE'),
  ('dill.detect',
   'D:\\Anaconda\\Lib\\site-packages\\dill\\detect.py',
   'PYMODULE'),
  ('dill.logger',
   'D:\\Anaconda\\Lib\\site-packages\\dill\\logger.py',
   'PYMODULE'),
  ('dill.objtypes',
   'D:\\Anaconda\\Lib\\site-packages\\dill\\objtypes.py',
   'PYMODULE'),
  ('dill.pointers',
   'D:\\Anaconda\\Lib\\site-packages\\dill\\pointers.py',
   'PYMODULE'),
  ('dill.session',
   'D:\\Anaconda\\Lib\\site-packages\\dill\\session.py',
   'PYMODULE'),
  ('dill.settings',
   'D:\\Anaconda\\Lib\\site-packages\\dill\\settings.py',
   'PYMODULE'),
  ('dill.source',
   'D:\\Anaconda\\Lib\\site-packages\\dill\\source.py',
   'PYMODULE'),
  ('dill.temp', 'D:\\Anaconda\\Lib\\site-packages\\dill\\temp.py', 'PYMODULE'),
  ('dis', 'D:\\Anaconda\\Lib\\dis.py', 'PYMODULE'),
  ('distutils', 'D:\\Anaconda\\Lib\\distutils\\__init__.py', 'PYMODULE'),
  ('distutils._msvccompiler',
   'D:\\Anaconda\\Lib\\distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('distutils.archive_util',
   'D:\\Anaconda\\Lib\\distutils\\archive_util.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   'D:\\Anaconda\\Lib\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('distutils.cmd', 'D:\\Anaconda\\Lib\\distutils\\cmd.py', 'PYMODULE'),
  ('distutils.command',
   'D:\\Anaconda\\Lib\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   'D:\\Anaconda\\Lib\\distutils\\command\\bdist.py',
   'PYMODULE'),
  ('distutils.command.build',
   'D:\\Anaconda\\Lib\\distutils\\command\\build.py',
   'PYMODULE'),
  ('distutils.command.build_ext',
   'D:\\Anaconda\\Lib\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('distutils.command.build_py',
   'D:\\Anaconda\\Lib\\distutils\\command\\build_py.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   'D:\\Anaconda\\Lib\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('distutils.config', 'D:\\Anaconda\\Lib\\distutils\\config.py', 'PYMODULE'),
  ('distutils.core', 'D:\\Anaconda\\Lib\\distutils\\core.py', 'PYMODULE'),
  ('distutils.debug', 'D:\\Anaconda\\Lib\\distutils\\debug.py', 'PYMODULE'),
  ('distutils.dep_util',
   'D:\\Anaconda\\Lib\\distutils\\dep_util.py',
   'PYMODULE'),
  ('distutils.dir_util',
   'D:\\Anaconda\\Lib\\distutils\\dir_util.py',
   'PYMODULE'),
  ('distutils.dist', 'D:\\Anaconda\\Lib\\distutils\\dist.py', 'PYMODULE'),
  ('distutils.errors', 'D:\\Anaconda\\Lib\\distutils\\errors.py', 'PYMODULE'),
  ('distutils.extension',
   'D:\\Anaconda\\Lib\\distutils\\extension.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   'D:\\Anaconda\\Lib\\distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('distutils.file_util',
   'D:\\Anaconda\\Lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.filelist',
   'D:\\Anaconda\\Lib\\distutils\\filelist.py',
   'PYMODULE'),
  ('distutils.log', 'D:\\Anaconda\\Lib\\distutils\\log.py', 'PYMODULE'),
  ('distutils.msvc9compiler',
   'D:\\Anaconda\\Lib\\distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('distutils.spawn', 'D:\\Anaconda\\Lib\\distutils\\spawn.py', 'PYMODULE'),
  ('distutils.sysconfig',
   'D:\\Anaconda\\Lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('distutils.text_file',
   'D:\\Anaconda\\Lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('distutils.util', 'D:\\Anaconda\\Lib\\distutils\\util.py', 'PYMODULE'),
  ('distutils.version', 'D:\\Anaconda\\Lib\\distutils\\version.py', 'PYMODULE'),
  ('distutils.versionpredicate',
   'D:\\Anaconda\\Lib\\distutils\\versionpredicate.py',
   'PYMODULE'),
  ('doctest', 'D:\\Anaconda\\Lib\\doctest.py', 'PYMODULE'),
  ('email', 'D:\\Anaconda\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\Anaconda\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\Anaconda\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr', 'D:\\Anaconda\\Lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('email._policybase', 'D:\\Anaconda\\Lib\\email\\_policybase.py', 'PYMODULE'),
  ('email.base64mime', 'D:\\Anaconda\\Lib\\email\\base64mime.py', 'PYMODULE'),
  ('email.charset', 'D:\\Anaconda\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'D:\\Anaconda\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders', 'D:\\Anaconda\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.errors', 'D:\\Anaconda\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser', 'D:\\Anaconda\\Lib\\email\\feedparser.py', 'PYMODULE'),
  ('email.generator', 'D:\\Anaconda\\Lib\\email\\generator.py', 'PYMODULE'),
  ('email.header', 'D:\\Anaconda\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'D:\\Anaconda\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators', 'D:\\Anaconda\\Lib\\email\\iterators.py', 'PYMODULE'),
  ('email.message', 'D:\\Anaconda\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.parser', 'D:\\Anaconda\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'D:\\Anaconda\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime', 'D:\\Anaconda\\Lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.utils', 'D:\\Anaconda\\Lib\\email\\utils.py', 'PYMODULE'),
  ('et_xmlfile',
   'D:\\Anaconda\\Lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'D:\\Anaconda\\Lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('fnmatch', 'D:\\Anaconda\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'D:\\Anaconda\\Lib\\fractions.py', 'PYMODULE'),
  ('frozenlist',
   'D:\\Anaconda\\Lib\\site-packages\\frozenlist\\__init__.py',
   'PYMODULE'),
  ('fsspec',
   'D:\\Anaconda\\Lib\\site-packages\\fsspec\\__init__.py',
   'PYMODULE'),
  ('fsspec._version',
   'D:\\Anaconda\\Lib\\site-packages\\fsspec\\_version.py',
   'PYMODULE'),
  ('fsspec.archive',
   'D:\\Anaconda\\Lib\\site-packages\\fsspec\\archive.py',
   'PYMODULE'),
  ('fsspec.asyn',
   'D:\\Anaconda\\Lib\\site-packages\\fsspec\\asyn.py',
   'PYMODULE'),
  ('fsspec.caching',
   'D:\\Anaconda\\Lib\\site-packages\\fsspec\\caching.py',
   'PYMODULE'),
  ('fsspec.callbacks',
   'D:\\Anaconda\\Lib\\site-packages\\fsspec\\callbacks.py',
   'PYMODULE'),
  ('fsspec.compression',
   'D:\\Anaconda\\Lib\\site-packages\\fsspec\\compression.py',
   'PYMODULE'),
  ('fsspec.config',
   'D:\\Anaconda\\Lib\\site-packages\\fsspec\\config.py',
   'PYMODULE'),
  ('fsspec.conftest',
   'D:\\Anaconda\\Lib\\site-packages\\fsspec\\conftest.py',
   'PYMODULE'),
  ('fsspec.core',
   'D:\\Anaconda\\Lib\\site-packages\\fsspec\\core.py',
   'PYMODULE'),
  ('fsspec.dircache',
   'D:\\Anaconda\\Lib\\site-packages\\fsspec\\dircache.py',
   'PYMODULE'),
  ('fsspec.exceptions',
   'D:\\Anaconda\\Lib\\site-packages\\fsspec\\exceptions.py',
   'PYMODULE'),
  ('fsspec.fuse',
   'D:\\Anaconda\\Lib\\site-packages\\fsspec\\fuse.py',
   'PYMODULE'),
  ('fsspec.generic',
   'D:\\Anaconda\\Lib\\site-packages\\fsspec\\generic.py',
   'PYMODULE'),
  ('fsspec.gui',
   'D:\\Anaconda\\Lib\\site-packages\\fsspec\\gui.py',
   'PYMODULE'),
  ('fsspec.implementations',
   'D:\\Anaconda\\Lib\\site-packages\\fsspec\\implementations\\__init__.py',
   'PYMODULE'),
  ('fsspec.implementations.arrow',
   'D:\\Anaconda\\Lib\\site-packages\\fsspec\\implementations\\arrow.py',
   'PYMODULE'),
  ('fsspec.implementations.cache_mapper',
   'D:\\Anaconda\\Lib\\site-packages\\fsspec\\implementations\\cache_mapper.py',
   'PYMODULE'),
  ('fsspec.implementations.cache_metadata',
   'D:\\Anaconda\\Lib\\site-packages\\fsspec\\implementations\\cache_metadata.py',
   'PYMODULE'),
  ('fsspec.implementations.cached',
   'D:\\Anaconda\\Lib\\site-packages\\fsspec\\implementations\\cached.py',
   'PYMODULE'),
  ('fsspec.implementations.dask',
   'D:\\Anaconda\\Lib\\site-packages\\fsspec\\implementations\\dask.py',
   'PYMODULE'),
  ('fsspec.implementations.data',
   'D:\\Anaconda\\Lib\\site-packages\\fsspec\\implementations\\data.py',
   'PYMODULE'),
  ('fsspec.implementations.dbfs',
   'D:\\Anaconda\\Lib\\site-packages\\fsspec\\implementations\\dbfs.py',
   'PYMODULE'),
  ('fsspec.implementations.dirfs',
   'D:\\Anaconda\\Lib\\site-packages\\fsspec\\implementations\\dirfs.py',
   'PYMODULE'),
  ('fsspec.implementations.ftp',
   'D:\\Anaconda\\Lib\\site-packages\\fsspec\\implementations\\ftp.py',
   'PYMODULE'),
  ('fsspec.implementations.git',
   'D:\\Anaconda\\Lib\\site-packages\\fsspec\\implementations\\git.py',
   'PYMODULE'),
  ('fsspec.implementations.github',
   'D:\\Anaconda\\Lib\\site-packages\\fsspec\\implementations\\github.py',
   'PYMODULE'),
  ('fsspec.implementations.http',
   'D:\\Anaconda\\Lib\\site-packages\\fsspec\\implementations\\http.py',
   'PYMODULE'),
  ('fsspec.implementations.jupyter',
   'D:\\Anaconda\\Lib\\site-packages\\fsspec\\implementations\\jupyter.py',
   'PYMODULE'),
  ('fsspec.implementations.libarchive',
   'D:\\Anaconda\\Lib\\site-packages\\fsspec\\implementations\\libarchive.py',
   'PYMODULE'),
  ('fsspec.implementations.local',
   'D:\\Anaconda\\Lib\\site-packages\\fsspec\\implementations\\local.py',
   'PYMODULE'),
  ('fsspec.implementations.memory',
   'D:\\Anaconda\\Lib\\site-packages\\fsspec\\implementations\\memory.py',
   'PYMODULE'),
  ('fsspec.implementations.reference',
   'D:\\Anaconda\\Lib\\site-packages\\fsspec\\implementations\\reference.py',
   'PYMODULE'),
  ('fsspec.implementations.sftp',
   'D:\\Anaconda\\Lib\\site-packages\\fsspec\\implementations\\sftp.py',
   'PYMODULE'),
  ('fsspec.implementations.smb',
   'D:\\Anaconda\\Lib\\site-packages\\fsspec\\implementations\\smb.py',
   'PYMODULE'),
  ('fsspec.implementations.tar',
   'D:\\Anaconda\\Lib\\site-packages\\fsspec\\implementations\\tar.py',
   'PYMODULE'),
  ('fsspec.implementations.webhdfs',
   'D:\\Anaconda\\Lib\\site-packages\\fsspec\\implementations\\webhdfs.py',
   'PYMODULE'),
  ('fsspec.implementations.zip',
   'D:\\Anaconda\\Lib\\site-packages\\fsspec\\implementations\\zip.py',
   'PYMODULE'),
  ('fsspec.mapping',
   'D:\\Anaconda\\Lib\\site-packages\\fsspec\\mapping.py',
   'PYMODULE'),
  ('fsspec.parquet',
   'D:\\Anaconda\\Lib\\site-packages\\fsspec\\parquet.py',
   'PYMODULE'),
  ('fsspec.registry',
   'D:\\Anaconda\\Lib\\site-packages\\fsspec\\registry.py',
   'PYMODULE'),
  ('fsspec.spec',
   'D:\\Anaconda\\Lib\\site-packages\\fsspec\\spec.py',
   'PYMODULE'),
  ('fsspec.transaction',
   'D:\\Anaconda\\Lib\\site-packages\\fsspec\\transaction.py',
   'PYMODULE'),
  ('fsspec.utils',
   'D:\\Anaconda\\Lib\\site-packages\\fsspec\\utils.py',
   'PYMODULE'),
  ('ftplib', 'D:\\Anaconda\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'D:\\Anaconda\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'D:\\Anaconda\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'D:\\Anaconda\\Lib\\gettext.py', 'PYMODULE'),
  ('glob', 'D:\\Anaconda\\Lib\\glob.py', 'PYMODULE'),
  ('gzip', 'D:\\Anaconda\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'D:\\Anaconda\\Lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'D:\\Anaconda\\Lib\\hmac.py', 'PYMODULE'),
  ('html', 'D:\\Anaconda\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'D:\\Anaconda\\Lib\\html\\entities.py', 'PYMODULE'),
  ('html.parser', 'D:\\Anaconda\\Lib\\html\\parser.py', 'PYMODULE'),
  ('http', 'D:\\Anaconda\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'D:\\Anaconda\\Lib\\http\\client.py', 'PYMODULE'),
  ('http.cookiejar', 'D:\\Anaconda\\Lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http.cookies', 'D:\\Anaconda\\Lib\\http\\cookies.py', 'PYMODULE'),
  ('http.server', 'D:\\Anaconda\\Lib\\http\\server.py', 'PYMODULE'),
  ('idna', 'D:\\Anaconda\\Lib\\site-packages\\idna\\__init__.py', 'PYMODULE'),
  ('idna.core', 'D:\\Anaconda\\Lib\\site-packages\\idna\\core.py', 'PYMODULE'),
  ('idna.idnadata',
   'D:\\Anaconda\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'D:\\Anaconda\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'D:\\Anaconda\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'D:\\Anaconda\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('imp', 'D:\\Anaconda\\Lib\\imp.py', 'PYMODULE'),
  ('importlib', 'D:\\Anaconda\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._abc', 'D:\\Anaconda\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\Anaconda\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\Anaconda\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc', 'D:\\Anaconda\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\Anaconda\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\Anaconda\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\Anaconda\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\Anaconda\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\Anaconda\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\Anaconda\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\Anaconda\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\Anaconda\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers', 'D:\\Anaconda\\Lib\\importlib\\readers.py', 'PYMODULE'),
  ('importlib.resources',
   'D:\\Anaconda\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\Anaconda\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\Anaconda\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\Anaconda\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'D:\\Anaconda\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\Anaconda\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\Anaconda\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util', 'D:\\Anaconda\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('importlib_metadata',
   'D:\\Anaconda\\Lib\\site-packages\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata._adapters',
   'D:\\Anaconda\\Lib\\site-packages\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib_metadata._collections',
   'D:\\Anaconda\\Lib\\site-packages\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('importlib_metadata._compat',
   'D:\\Anaconda\\Lib\\site-packages\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('importlib_metadata._functools',
   'D:\\Anaconda\\Lib\\site-packages\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('importlib_metadata._itertools',
   'D:\\Anaconda\\Lib\\site-packages\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib_metadata._meta',
   'D:\\Anaconda\\Lib\\site-packages\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('importlib_metadata._py39compat',
   'D:\\Anaconda\\Lib\\site-packages\\importlib_metadata\\_py39compat.py',
   'PYMODULE'),
  ('importlib_metadata._text',
   'D:\\Anaconda\\Lib\\site-packages\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('incremental',
   'D:\\Anaconda\\Lib\\site-packages\\incremental\\__init__.py',
   'PYMODULE'),
  ('incremental._version',
   'D:\\Anaconda\\Lib\\site-packages\\incremental\\_version.py',
   'PYMODULE'),
  ('iniconfig',
   'D:\\Anaconda\\Lib\\site-packages\\iniconfig\\__init__.py',
   'PYMODULE'),
  ('inspect', 'D:\\Anaconda\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'D:\\Anaconda\\Lib\\ipaddress.py', 'PYMODULE'),
  ('ipykernel',
   'D:\\Anaconda\\Lib\\site-packages\\ipykernel\\__init__.py',
   'PYMODULE'),
  ('ipykernel._eventloop_macos',
   'D:\\Anaconda\\Lib\\site-packages\\ipykernel\\_eventloop_macos.py',
   'PYMODULE'),
  ('ipykernel._version',
   'D:\\Anaconda\\Lib\\site-packages\\ipykernel\\_version.py',
   'PYMODULE'),
  ('ipykernel.comm',
   'D:\\Anaconda\\Lib\\site-packages\\ipykernel\\comm\\__init__.py',
   'PYMODULE'),
  ('ipykernel.comm.comm',
   'D:\\Anaconda\\Lib\\site-packages\\ipykernel\\comm\\comm.py',
   'PYMODULE'),
  ('ipykernel.comm.manager',
   'D:\\Anaconda\\Lib\\site-packages\\ipykernel\\comm\\manager.py',
   'PYMODULE'),
  ('ipykernel.compiler',
   'D:\\Anaconda\\Lib\\site-packages\\ipykernel\\compiler.py',
   'PYMODULE'),
  ('ipykernel.connect',
   'D:\\Anaconda\\Lib\\site-packages\\ipykernel\\connect.py',
   'PYMODULE'),
  ('ipykernel.control',
   'D:\\Anaconda\\Lib\\site-packages\\ipykernel\\control.py',
   'PYMODULE'),
  ('ipykernel.debugger',
   'D:\\Anaconda\\Lib\\site-packages\\ipykernel\\debugger.py',
   'PYMODULE'),
  ('ipykernel.displayhook',
   'D:\\Anaconda\\Lib\\site-packages\\ipykernel\\displayhook.py',
   'PYMODULE'),
  ('ipykernel.eventloops',
   'D:\\Anaconda\\Lib\\site-packages\\ipykernel\\eventloops.py',
   'PYMODULE'),
  ('ipykernel.gui',
   'D:\\Anaconda\\Lib\\site-packages\\ipykernel\\gui\\__init__.py',
   'PYMODULE'),
  ('ipykernel.gui.gtk3embed',
   'D:\\Anaconda\\Lib\\site-packages\\ipykernel\\gui\\gtk3embed.py',
   'PYMODULE'),
  ('ipykernel.gui.gtkembed',
   'D:\\Anaconda\\Lib\\site-packages\\ipykernel\\gui\\gtkembed.py',
   'PYMODULE'),
  ('ipykernel.heartbeat',
   'D:\\Anaconda\\Lib\\site-packages\\ipykernel\\heartbeat.py',
   'PYMODULE'),
  ('ipykernel.iostream',
   'D:\\Anaconda\\Lib\\site-packages\\ipykernel\\iostream.py',
   'PYMODULE'),
  ('ipykernel.ipkernel',
   'D:\\Anaconda\\Lib\\site-packages\\ipykernel\\ipkernel.py',
   'PYMODULE'),
  ('ipykernel.jsonutil',
   'D:\\Anaconda\\Lib\\site-packages\\ipykernel\\jsonutil.py',
   'PYMODULE'),
  ('ipykernel.kernelapp',
   'D:\\Anaconda\\Lib\\site-packages\\ipykernel\\kernelapp.py',
   'PYMODULE'),
  ('ipykernel.kernelbase',
   'D:\\Anaconda\\Lib\\site-packages\\ipykernel\\kernelbase.py',
   'PYMODULE'),
  ('ipykernel.kernelspec',
   'D:\\Anaconda\\Lib\\site-packages\\ipykernel\\kernelspec.py',
   'PYMODULE'),
  ('ipykernel.parentpoller',
   'D:\\Anaconda\\Lib\\site-packages\\ipykernel\\parentpoller.py',
   'PYMODULE'),
  ('ipykernel.pickleutil',
   'D:\\Anaconda\\Lib\\site-packages\\ipykernel\\pickleutil.py',
   'PYMODULE'),
  ('ipykernel.pylab',
   'D:\\Anaconda\\Lib\\site-packages\\ipykernel\\pylab\\__init__.py',
   'PYMODULE'),
  ('ipykernel.pylab.backend_inline',
   'D:\\Anaconda\\Lib\\site-packages\\ipykernel\\pylab\\backend_inline.py',
   'PYMODULE'),
  ('ipykernel.serialize',
   'D:\\Anaconda\\Lib\\site-packages\\ipykernel\\serialize.py',
   'PYMODULE'),
  ('ipykernel.trio_runner',
   'D:\\Anaconda\\Lib\\site-packages\\ipykernel\\trio_runner.py',
   'PYMODULE'),
  ('ipykernel.zmqshell',
   'D:\\Anaconda\\Lib\\site-packages\\ipykernel\\zmqshell.py',
   'PYMODULE'),
  ('ipywidgets',
   'D:\\Anaconda\\Lib\\site-packages\\ipywidgets\\__init__.py',
   'PYMODULE'),
  ('ipywidgets._version',
   'D:\\Anaconda\\Lib\\site-packages\\ipywidgets\\_version.py',
   'PYMODULE'),
  ('ipywidgets.widgets',
   'D:\\Anaconda\\Lib\\site-packages\\ipywidgets\\widgets\\__init__.py',
   'PYMODULE'),
  ('ipywidgets.widgets.docutils',
   'D:\\Anaconda\\Lib\\site-packages\\ipywidgets\\widgets\\docutils.py',
   'PYMODULE'),
  ('ipywidgets.widgets.domwidget',
   'D:\\Anaconda\\Lib\\site-packages\\ipywidgets\\widgets\\domwidget.py',
   'PYMODULE'),
  ('ipywidgets.widgets.interaction',
   'D:\\Anaconda\\Lib\\site-packages\\ipywidgets\\widgets\\interaction.py',
   'PYMODULE'),
  ('ipywidgets.widgets.trait_types',
   'D:\\Anaconda\\Lib\\site-packages\\ipywidgets\\widgets\\trait_types.py',
   'PYMODULE'),
  ('ipywidgets.widgets.utils',
   'D:\\Anaconda\\Lib\\site-packages\\ipywidgets\\widgets\\utils.py',
   'PYMODULE'),
  ('ipywidgets.widgets.valuewidget',
   'D:\\Anaconda\\Lib\\site-packages\\ipywidgets\\widgets\\valuewidget.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget',
   'D:\\Anaconda\\Lib\\site-packages\\ipywidgets\\widgets\\widget.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_bool',
   'D:\\Anaconda\\Lib\\site-packages\\ipywidgets\\widgets\\widget_bool.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_box',
   'D:\\Anaconda\\Lib\\site-packages\\ipywidgets\\widgets\\widget_box.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_button',
   'D:\\Anaconda\\Lib\\site-packages\\ipywidgets\\widgets\\widget_button.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_color',
   'D:\\Anaconda\\Lib\\site-packages\\ipywidgets\\widgets\\widget_color.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_controller',
   'D:\\Anaconda\\Lib\\site-packages\\ipywidgets\\widgets\\widget_controller.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_core',
   'D:\\Anaconda\\Lib\\site-packages\\ipywidgets\\widgets\\widget_core.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_date',
   'D:\\Anaconda\\Lib\\site-packages\\ipywidgets\\widgets\\widget_date.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_datetime',
   'D:\\Anaconda\\Lib\\site-packages\\ipywidgets\\widgets\\widget_datetime.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_description',
   'D:\\Anaconda\\Lib\\site-packages\\ipywidgets\\widgets\\widget_description.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_float',
   'D:\\Anaconda\\Lib\\site-packages\\ipywidgets\\widgets\\widget_float.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_int',
   'D:\\Anaconda\\Lib\\site-packages\\ipywidgets\\widgets\\widget_int.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_layout',
   'D:\\Anaconda\\Lib\\site-packages\\ipywidgets\\widgets\\widget_layout.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_link',
   'D:\\Anaconda\\Lib\\site-packages\\ipywidgets\\widgets\\widget_link.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_media',
   'D:\\Anaconda\\Lib\\site-packages\\ipywidgets\\widgets\\widget_media.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_output',
   'D:\\Anaconda\\Lib\\site-packages\\ipywidgets\\widgets\\widget_output.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_selection',
   'D:\\Anaconda\\Lib\\site-packages\\ipywidgets\\widgets\\widget_selection.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_selectioncontainer',
   'D:\\Anaconda\\Lib\\site-packages\\ipywidgets\\widgets\\widget_selectioncontainer.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_string',
   'D:\\Anaconda\\Lib\\site-packages\\ipywidgets\\widgets\\widget_string.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_style',
   'D:\\Anaconda\\Lib\\site-packages\\ipywidgets\\widgets\\widget_style.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_tagsinput',
   'D:\\Anaconda\\Lib\\site-packages\\ipywidgets\\widgets\\widget_tagsinput.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_templates',
   'D:\\Anaconda\\Lib\\site-packages\\ipywidgets\\widgets\\widget_templates.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_time',
   'D:\\Anaconda\\Lib\\site-packages\\ipywidgets\\widgets\\widget_time.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_upload',
   'D:\\Anaconda\\Lib\\site-packages\\ipywidgets\\widgets\\widget_upload.py',
   'PYMODULE'),
  ('json', 'D:\\Anaconda\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder', 'D:\\Anaconda\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.encoder', 'D:\\Anaconda\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.scanner', 'D:\\Anaconda\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('jupyter_client',
   'D:\\Anaconda\\Lib\\site-packages\\jupyter_client\\__init__.py',
   'PYMODULE'),
  ('jupyter_client._version',
   'D:\\Anaconda\\Lib\\site-packages\\jupyter_client\\_version.py',
   'PYMODULE'),
  ('jupyter_client.adapter',
   'D:\\Anaconda\\Lib\\site-packages\\jupyter_client\\adapter.py',
   'PYMODULE'),
  ('jupyter_client.asynchronous',
   'D:\\Anaconda\\Lib\\site-packages\\jupyter_client\\asynchronous\\__init__.py',
   'PYMODULE'),
  ('jupyter_client.asynchronous.client',
   'D:\\Anaconda\\Lib\\site-packages\\jupyter_client\\asynchronous\\client.py',
   'PYMODULE'),
  ('jupyter_client.blocking',
   'D:\\Anaconda\\Lib\\site-packages\\jupyter_client\\blocking\\__init__.py',
   'PYMODULE'),
  ('jupyter_client.blocking.client',
   'D:\\Anaconda\\Lib\\site-packages\\jupyter_client\\blocking\\client.py',
   'PYMODULE'),
  ('jupyter_client.channels',
   'D:\\Anaconda\\Lib\\site-packages\\jupyter_client\\channels.py',
   'PYMODULE'),
  ('jupyter_client.channelsabc',
   'D:\\Anaconda\\Lib\\site-packages\\jupyter_client\\channelsabc.py',
   'PYMODULE'),
  ('jupyter_client.client',
   'D:\\Anaconda\\Lib\\site-packages\\jupyter_client\\client.py',
   'PYMODULE'),
  ('jupyter_client.clientabc',
   'D:\\Anaconda\\Lib\\site-packages\\jupyter_client\\clientabc.py',
   'PYMODULE'),
  ('jupyter_client.connect',
   'D:\\Anaconda\\Lib\\site-packages\\jupyter_client\\connect.py',
   'PYMODULE'),
  ('jupyter_client.jsonutil',
   'D:\\Anaconda\\Lib\\site-packages\\jupyter_client\\jsonutil.py',
   'PYMODULE'),
  ('jupyter_client.kernelspec',
   'D:\\Anaconda\\Lib\\site-packages\\jupyter_client\\kernelspec.py',
   'PYMODULE'),
  ('jupyter_client.launcher',
   'D:\\Anaconda\\Lib\\site-packages\\jupyter_client\\launcher.py',
   'PYMODULE'),
  ('jupyter_client.localinterfaces',
   'D:\\Anaconda\\Lib\\site-packages\\jupyter_client\\localinterfaces.py',
   'PYMODULE'),
  ('jupyter_client.manager',
   'D:\\Anaconda\\Lib\\site-packages\\jupyter_client\\manager.py',
   'PYMODULE'),
  ('jupyter_client.managerabc',
   'D:\\Anaconda\\Lib\\site-packages\\jupyter_client\\managerabc.py',
   'PYMODULE'),
  ('jupyter_client.multikernelmanager',
   'D:\\Anaconda\\Lib\\site-packages\\jupyter_client\\multikernelmanager.py',
   'PYMODULE'),
  ('jupyter_client.provisioning',
   'D:\\Anaconda\\Lib\\site-packages\\jupyter_client\\provisioning\\__init__.py',
   'PYMODULE'),
  ('jupyter_client.provisioning.factory',
   'D:\\Anaconda\\Lib\\site-packages\\jupyter_client\\provisioning\\factory.py',
   'PYMODULE'),
  ('jupyter_client.provisioning.local_provisioner',
   'D:\\Anaconda\\Lib\\site-packages\\jupyter_client\\provisioning\\local_provisioner.py',
   'PYMODULE'),
  ('jupyter_client.provisioning.provisioner_base',
   'D:\\Anaconda\\Lib\\site-packages\\jupyter_client\\provisioning\\provisioner_base.py',
   'PYMODULE'),
  ('jupyter_client.session',
   'D:\\Anaconda\\Lib\\site-packages\\jupyter_client\\session.py',
   'PYMODULE'),
  ('jupyter_client.ssh',
   'D:\\Anaconda\\Lib\\site-packages\\jupyter_client\\ssh\\__init__.py',
   'PYMODULE'),
  ('jupyter_client.ssh.forward',
   'D:\\Anaconda\\Lib\\site-packages\\jupyter_client\\ssh\\forward.py',
   'PYMODULE'),
  ('jupyter_client.ssh.tunnel',
   'D:\\Anaconda\\Lib\\site-packages\\jupyter_client\\ssh\\tunnel.py',
   'PYMODULE'),
  ('jupyter_client.utils',
   'D:\\Anaconda\\Lib\\site-packages\\jupyter_client\\utils.py',
   'PYMODULE'),
  ('jupyter_client.win_interrupt',
   'D:\\Anaconda\\Lib\\site-packages\\jupyter_client\\win_interrupt.py',
   'PYMODULE'),
  ('jupyter_core',
   'D:\\Anaconda\\Lib\\site-packages\\jupyter_core\\__init__.py',
   'PYMODULE'),
  ('jupyter_core.paths',
   'D:\\Anaconda\\Lib\\site-packages\\jupyter_core\\paths.py',
   'PYMODULE'),
  ('jupyter_core.utils',
   'D:\\Anaconda\\Lib\\site-packages\\jupyter_core\\utils\\__init__.py',
   'PYMODULE'),
  ('jupyter_core.version',
   'D:\\Anaconda\\Lib\\site-packages\\jupyter_core\\version.py',
   'PYMODULE'),
  ('libarchive',
   'D:\\Anaconda\\Lib\\site-packages\\libarchive\\__init__.py',
   'PYMODULE'),
  ('libarchive.entry',
   'D:\\Anaconda\\Lib\\site-packages\\libarchive\\entry.py',
   'PYMODULE'),
  ('libarchive.exception',
   'D:\\Anaconda\\Lib\\site-packages\\libarchive\\exception.py',
   'PYMODULE'),
  ('libarchive.extract',
   'D:\\Anaconda\\Lib\\site-packages\\libarchive\\extract.py',
   'PYMODULE'),
  ('libarchive.ffi',
   'D:\\Anaconda\\Lib\\site-packages\\libarchive\\ffi.py',
   'PYMODULE'),
  ('libarchive.read',
   'D:\\Anaconda\\Lib\\site-packages\\libarchive\\read.py',
   'PYMODULE'),
  ('libarchive.write',
   'D:\\Anaconda\\Lib\\site-packages\\libarchive\\write.py',
   'PYMODULE'),
  ('llvmlite',
   'D:\\Anaconda\\Lib\\site-packages\\llvmlite\\__init__.py',
   'PYMODULE'),
  ('llvmlite._version',
   'D:\\Anaconda\\Lib\\site-packages\\llvmlite\\_version.py',
   'PYMODULE'),
  ('llvmlite.binding',
   'D:\\Anaconda\\Lib\\site-packages\\llvmlite\\binding\\__init__.py',
   'PYMODULE'),
  ('llvmlite.binding.analysis',
   'D:\\Anaconda\\Lib\\site-packages\\llvmlite\\binding\\analysis.py',
   'PYMODULE'),
  ('llvmlite.binding.common',
   'D:\\Anaconda\\Lib\\site-packages\\llvmlite\\binding\\common.py',
   'PYMODULE'),
  ('llvmlite.binding.context',
   'D:\\Anaconda\\Lib\\site-packages\\llvmlite\\binding\\context.py',
   'PYMODULE'),
  ('llvmlite.binding.dylib',
   'D:\\Anaconda\\Lib\\site-packages\\llvmlite\\binding\\dylib.py',
   'PYMODULE'),
  ('llvmlite.binding.executionengine',
   'D:\\Anaconda\\Lib\\site-packages\\llvmlite\\binding\\executionengine.py',
   'PYMODULE'),
  ('llvmlite.binding.ffi',
   'D:\\Anaconda\\Lib\\site-packages\\llvmlite\\binding\\ffi.py',
   'PYMODULE'),
  ('llvmlite.binding.initfini',
   'D:\\Anaconda\\Lib\\site-packages\\llvmlite\\binding\\initfini.py',
   'PYMODULE'),
  ('llvmlite.binding.linker',
   'D:\\Anaconda\\Lib\\site-packages\\llvmlite\\binding\\linker.py',
   'PYMODULE'),
  ('llvmlite.binding.module',
   'D:\\Anaconda\\Lib\\site-packages\\llvmlite\\binding\\module.py',
   'PYMODULE'),
  ('llvmlite.binding.object_file',
   'D:\\Anaconda\\Lib\\site-packages\\llvmlite\\binding\\object_file.py',
   'PYMODULE'),
  ('llvmlite.binding.options',
   'D:\\Anaconda\\Lib\\site-packages\\llvmlite\\binding\\options.py',
   'PYMODULE'),
  ('llvmlite.binding.passmanagers',
   'D:\\Anaconda\\Lib\\site-packages\\llvmlite\\binding\\passmanagers.py',
   'PYMODULE'),
  ('llvmlite.binding.targets',
   'D:\\Anaconda\\Lib\\site-packages\\llvmlite\\binding\\targets.py',
   'PYMODULE'),
  ('llvmlite.binding.transforms',
   'D:\\Anaconda\\Lib\\site-packages\\llvmlite\\binding\\transforms.py',
   'PYMODULE'),
  ('llvmlite.binding.value',
   'D:\\Anaconda\\Lib\\site-packages\\llvmlite\\binding\\value.py',
   'PYMODULE'),
  ('llvmlite.ir',
   'D:\\Anaconda\\Lib\\site-packages\\llvmlite\\ir\\__init__.py',
   'PYMODULE'),
  ('llvmlite.ir._utils',
   'D:\\Anaconda\\Lib\\site-packages\\llvmlite\\ir\\_utils.py',
   'PYMODULE'),
  ('llvmlite.ir.builder',
   'D:\\Anaconda\\Lib\\site-packages\\llvmlite\\ir\\builder.py',
   'PYMODULE'),
  ('llvmlite.ir.context',
   'D:\\Anaconda\\Lib\\site-packages\\llvmlite\\ir\\context.py',
   'PYMODULE'),
  ('llvmlite.ir.instructions',
   'D:\\Anaconda\\Lib\\site-packages\\llvmlite\\ir\\instructions.py',
   'PYMODULE'),
  ('llvmlite.ir.module',
   'D:\\Anaconda\\Lib\\site-packages\\llvmlite\\ir\\module.py',
   'PYMODULE'),
  ('llvmlite.ir.transforms',
   'D:\\Anaconda\\Lib\\site-packages\\llvmlite\\ir\\transforms.py',
   'PYMODULE'),
  ('llvmlite.ir.types',
   'D:\\Anaconda\\Lib\\site-packages\\llvmlite\\ir\\types.py',
   'PYMODULE'),
  ('llvmlite.ir.values',
   'D:\\Anaconda\\Lib\\site-packages\\llvmlite\\ir\\values.py',
   'PYMODULE'),
  ('llvmlite.utils',
   'D:\\Anaconda\\Lib\\site-packages\\llvmlite\\utils.py',
   'PYMODULE'),
  ('logging', 'D:\\Anaconda\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('logging.config', 'D:\\Anaconda\\Lib\\logging\\config.py', 'PYMODULE'),
  ('logging.handlers', 'D:\\Anaconda\\Lib\\logging\\handlers.py', 'PYMODULE'),
  ('lz4', 'D:\\Anaconda\\Lib\\site-packages\\lz4\\__init__.py', 'PYMODULE'),
  ('lz4.frame',
   'D:\\Anaconda\\Lib\\site-packages\\lz4\\frame\\__init__.py',
   'PYMODULE'),
  ('lz4.version',
   'D:\\Anaconda\\Lib\\site-packages\\lz4\\version.py',
   'PYMODULE'),
  ('lzma', 'D:\\Anaconda\\Lib\\lzma.py', 'PYMODULE'),
  ('markupsafe',
   'D:\\Anaconda\\Lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'D:\\Anaconda\\Lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('matplotlib_inline',
   'D:\\Anaconda\\Lib\\site-packages\\matplotlib_inline\\__init__.py',
   'PYMODULE'),
  ('matplotlib_inline.backend_inline',
   'D:\\Anaconda\\Lib\\site-packages\\matplotlib_inline\\backend_inline.py',
   'PYMODULE'),
  ('matplotlib_inline.config',
   'D:\\Anaconda\\Lib\\site-packages\\matplotlib_inline\\config.py',
   'PYMODULE'),
  ('mimetypes', 'D:\\Anaconda\\Lib\\mimetypes.py', 'PYMODULE'),
  ('mkl', 'D:\\Anaconda\\Lib\\site-packages\\mkl\\__init__.py', 'PYMODULE'),
  ('multidict',
   'D:\\Anaconda\\Lib\\site-packages\\multidict\\__init__.py',
   'PYMODULE'),
  ('multidict._abc',
   'D:\\Anaconda\\Lib\\site-packages\\multidict\\_abc.py',
   'PYMODULE'),
  ('multidict._compat',
   'D:\\Anaconda\\Lib\\site-packages\\multidict\\_compat.py',
   'PYMODULE'),
  ('multidict._multidict_py',
   'D:\\Anaconda\\Lib\\site-packages\\multidict\\_multidict_py.py',
   'PYMODULE'),
  ('multiprocessing',
   'D:\\Anaconda\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\Anaconda\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'D:\\Anaconda\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\Anaconda\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\Anaconda\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\Anaconda\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\Anaconda\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\Anaconda\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\Anaconda\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\Anaconda\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\Anaconda\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\Anaconda\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\Anaconda\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\Anaconda\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\Anaconda\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\Anaconda\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\Anaconda\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\Anaconda\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\Anaconda\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\Anaconda\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\Anaconda\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\Anaconda\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\Anaconda\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('nest_asyncio',
   'D:\\Anaconda\\Lib\\site-packages\\nest_asyncio.py',
   'PYMODULE'),
  ('netrc', 'D:\\Anaconda\\Lib\\netrc.py', 'PYMODULE'),
  ('ntsecuritycon',
   'D:\\Anaconda\\Lib\\site-packages\\win32\\lib\\ntsecuritycon.py',
   'PYMODULE'),
  ('nturl2path', 'D:\\Anaconda\\Lib\\nturl2path.py', 'PYMODULE'),
  ('numba', 'D:\\Anaconda\\Lib\\site-packages\\numba\\__init__.py', 'PYMODULE'),
  ('numba._version',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\_version.py',
   'PYMODULE'),
  ('numba.cext',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cext\\__init__.py',
   'PYMODULE'),
  ('numba.cloudpickle',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cloudpickle\\__init__.py',
   'PYMODULE'),
  ('numba.cloudpickle.cloudpickle',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cloudpickle\\cloudpickle.py',
   'PYMODULE'),
  ('numba.cloudpickle.cloudpickle_fast',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cloudpickle\\cloudpickle_fast.py',
   'PYMODULE'),
  ('numba.cloudpickle.compat',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cloudpickle\\compat.py',
   'PYMODULE'),
  ('numba.core',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\__init__.py',
   'PYMODULE'),
  ('numba.core.analysis',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\analysis.py',
   'PYMODULE'),
  ('numba.core.annotations',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\annotations\\__init__.py',
   'PYMODULE'),
  ('numba.core.annotations.pretty_annotate',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\annotations\\pretty_annotate.py',
   'PYMODULE'),
  ('numba.core.annotations.type_annotations',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\annotations\\type_annotations.py',
   'PYMODULE'),
  ('numba.core.base',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\base.py',
   'PYMODULE'),
  ('numba.core.boxing',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\boxing.py',
   'PYMODULE'),
  ('numba.core.bytecode',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\bytecode.py',
   'PYMODULE'),
  ('numba.core.byteflow',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\byteflow.py',
   'PYMODULE'),
  ('numba.core.caching',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\caching.py',
   'PYMODULE'),
  ('numba.core.callconv',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\callconv.py',
   'PYMODULE'),
  ('numba.core.callwrapper',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\callwrapper.py',
   'PYMODULE'),
  ('numba.core.ccallback',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\ccallback.py',
   'PYMODULE'),
  ('numba.core.cgutils',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\cgutils.py',
   'PYMODULE'),
  ('numba.core.codegen',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\codegen.py',
   'PYMODULE'),
  ('numba.core.compiler',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\compiler.py',
   'PYMODULE'),
  ('numba.core.compiler_lock',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\compiler_lock.py',
   'PYMODULE'),
  ('numba.core.compiler_machinery',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\compiler_machinery.py',
   'PYMODULE'),
  ('numba.core.config',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\config.py',
   'PYMODULE'),
  ('numba.core.consts',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\consts.py',
   'PYMODULE'),
  ('numba.core.controlflow',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\controlflow.py',
   'PYMODULE'),
  ('numba.core.cpu',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\cpu.py',
   'PYMODULE'),
  ('numba.core.cpu_options',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\cpu_options.py',
   'PYMODULE'),
  ('numba.core.datamodel',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\datamodel\\__init__.py',
   'PYMODULE'),
  ('numba.core.datamodel.manager',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\datamodel\\manager.py',
   'PYMODULE'),
  ('numba.core.datamodel.models',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\datamodel\\models.py',
   'PYMODULE'),
  ('numba.core.datamodel.packer',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\datamodel\\packer.py',
   'PYMODULE'),
  ('numba.core.datamodel.registry',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\datamodel\\registry.py',
   'PYMODULE'),
  ('numba.core.debuginfo',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\debuginfo.py',
   'PYMODULE'),
  ('numba.core.decorators',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\decorators.py',
   'PYMODULE'),
  ('numba.core.descriptors',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\descriptors.py',
   'PYMODULE'),
  ('numba.core.dispatcher',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\dispatcher.py',
   'PYMODULE'),
  ('numba.core.entrypoints',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\entrypoints.py',
   'PYMODULE'),
  ('numba.core.environment',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\environment.py',
   'PYMODULE'),
  ('numba.core.errors',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\errors.py',
   'PYMODULE'),
  ('numba.core.event',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\event.py',
   'PYMODULE'),
  ('numba.core.extending',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\extending.py',
   'PYMODULE'),
  ('numba.core.externals',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\externals.py',
   'PYMODULE'),
  ('numba.core.fastmathpass',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\fastmathpass.py',
   'PYMODULE'),
  ('numba.core.funcdesc',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\funcdesc.py',
   'PYMODULE'),
  ('numba.core.generators',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\generators.py',
   'PYMODULE'),
  ('numba.core.imputils',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\imputils.py',
   'PYMODULE'),
  ('numba.core.inline_closurecall',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\inline_closurecall.py',
   'PYMODULE'),
  ('numba.core.interpreter',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\interpreter.py',
   'PYMODULE'),
  ('numba.core.intrinsics',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\intrinsics.py',
   'PYMODULE'),
  ('numba.core.ir',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\ir.py',
   'PYMODULE'),
  ('numba.core.ir_utils',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\ir_utils.py',
   'PYMODULE'),
  ('numba.core.itanium_mangler',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\itanium_mangler.py',
   'PYMODULE'),
  ('numba.core.llvm_bindings',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\llvm_bindings.py',
   'PYMODULE'),
  ('numba.core.lowering',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\lowering.py',
   'PYMODULE'),
  ('numba.core.object_mode_passes',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\object_mode_passes.py',
   'PYMODULE'),
  ('numba.core.optional',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\optional.py',
   'PYMODULE'),
  ('numba.core.options',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\options.py',
   'PYMODULE'),
  ('numba.core.postproc',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\postproc.py',
   'PYMODULE'),
  ('numba.core.pylowering',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\pylowering.py',
   'PYMODULE'),
  ('numba.core.pythonapi',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\pythonapi.py',
   'PYMODULE'),
  ('numba.core.registry',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\registry.py',
   'PYMODULE'),
  ('numba.core.removerefctpass',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\removerefctpass.py',
   'PYMODULE'),
  ('numba.core.retarget',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\retarget.py',
   'PYMODULE'),
  ('numba.core.rewrites',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\rewrites\\__init__.py',
   'PYMODULE'),
  ('numba.core.rewrites.ir_print',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\rewrites\\ir_print.py',
   'PYMODULE'),
  ('numba.core.rewrites.registry',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\rewrites\\registry.py',
   'PYMODULE'),
  ('numba.core.rewrites.static_binop',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\rewrites\\static_binop.py',
   'PYMODULE'),
  ('numba.core.rewrites.static_getitem',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\rewrites\\static_getitem.py',
   'PYMODULE'),
  ('numba.core.rewrites.static_raise',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\rewrites\\static_raise.py',
   'PYMODULE'),
  ('numba.core.runtime',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\runtime\\__init__.py',
   'PYMODULE'),
  ('numba.core.runtime.context',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\runtime\\context.py',
   'PYMODULE'),
  ('numba.core.runtime.nrt',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\runtime\\nrt.py',
   'PYMODULE'),
  ('numba.core.runtime.nrtdynmod',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\runtime\\nrtdynmod.py',
   'PYMODULE'),
  ('numba.core.runtime.nrtopt',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\runtime\\nrtopt.py',
   'PYMODULE'),
  ('numba.core.serialize',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\serialize.py',
   'PYMODULE'),
  ('numba.core.sigutils',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\sigutils.py',
   'PYMODULE'),
  ('numba.core.ssa',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\ssa.py',
   'PYMODULE'),
  ('numba.core.target_extension',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\target_extension.py',
   'PYMODULE'),
  ('numba.core.targetconfig',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\targetconfig.py',
   'PYMODULE'),
  ('numba.core.tracing',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\tracing.py',
   'PYMODULE'),
  ('numba.core.transforms',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\transforms.py',
   'PYMODULE'),
  ('numba.core.typeconv',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\typeconv\\__init__.py',
   'PYMODULE'),
  ('numba.core.typeconv.castgraph',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\typeconv\\castgraph.py',
   'PYMODULE'),
  ('numba.core.typeconv.rules',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\typeconv\\rules.py',
   'PYMODULE'),
  ('numba.core.typeconv.typeconv',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\typeconv\\typeconv.py',
   'PYMODULE'),
  ('numba.core.typed_passes',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\typed_passes.py',
   'PYMODULE'),
  ('numba.core.typeinfer',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\typeinfer.py',
   'PYMODULE'),
  ('numba.core.types',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\types\\__init__.py',
   'PYMODULE'),
  ('numba.core.types.abstract',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\types\\abstract.py',
   'PYMODULE'),
  ('numba.core.types.common',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\types\\common.py',
   'PYMODULE'),
  ('numba.core.types.containers',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\types\\containers.py',
   'PYMODULE'),
  ('numba.core.types.function_type',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\types\\function_type.py',
   'PYMODULE'),
  ('numba.core.types.functions',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\types\\functions.py',
   'PYMODULE'),
  ('numba.core.types.iterators',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\types\\iterators.py',
   'PYMODULE'),
  ('numba.core.types.misc',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\types\\misc.py',
   'PYMODULE'),
  ('numba.core.types.npytypes',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\types\\npytypes.py',
   'PYMODULE'),
  ('numba.core.types.scalars',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\types\\scalars.py',
   'PYMODULE'),
  ('numba.core.typing',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\typing\\__init__.py',
   'PYMODULE'),
  ('numba.core.typing.arraydecl',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\typing\\arraydecl.py',
   'PYMODULE'),
  ('numba.core.typing.asnumbatype',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\typing\\asnumbatype.py',
   'PYMODULE'),
  ('numba.core.typing.bufproto',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\typing\\bufproto.py',
   'PYMODULE'),
  ('numba.core.typing.builtins',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\typing\\builtins.py',
   'PYMODULE'),
  ('numba.core.typing.cffi_utils',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\typing\\cffi_utils.py',
   'PYMODULE'),
  ('numba.core.typing.cmathdecl',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\typing\\cmathdecl.py',
   'PYMODULE'),
  ('numba.core.typing.collections',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\typing\\collections.py',
   'PYMODULE'),
  ('numba.core.typing.context',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\typing\\context.py',
   'PYMODULE'),
  ('numba.core.typing.ctypes_utils',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\typing\\ctypes_utils.py',
   'PYMODULE'),
  ('numba.core.typing.dictdecl',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\typing\\dictdecl.py',
   'PYMODULE'),
  ('numba.core.typing.enumdecl',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\typing\\enumdecl.py',
   'PYMODULE'),
  ('numba.core.typing.listdecl',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\typing\\listdecl.py',
   'PYMODULE'),
  ('numba.core.typing.mathdecl',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\typing\\mathdecl.py',
   'PYMODULE'),
  ('numba.core.typing.npdatetime',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\typing\\npdatetime.py',
   'PYMODULE'),
  ('numba.core.typing.npydecl',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\typing\\npydecl.py',
   'PYMODULE'),
  ('numba.core.typing.setdecl',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\typing\\setdecl.py',
   'PYMODULE'),
  ('numba.core.typing.templates',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\typing\\templates.py',
   'PYMODULE'),
  ('numba.core.typing.typeof',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\typing\\typeof.py',
   'PYMODULE'),
  ('numba.core.unsafe',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\unsafe\\__init__.py',
   'PYMODULE'),
  ('numba.core.unsafe.bytes',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\unsafe\\bytes.py',
   'PYMODULE'),
  ('numba.core.unsafe.eh',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\unsafe\\eh.py',
   'PYMODULE'),
  ('numba.core.untyped_passes',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\untyped_passes.py',
   'PYMODULE'),
  ('numba.core.utils',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\utils.py',
   'PYMODULE'),
  ('numba.core.withcontexts',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\core\\withcontexts.py',
   'PYMODULE'),
  ('numba.cpython',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cpython\\__init__.py',
   'PYMODULE'),
  ('numba.cpython.builtins',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cpython\\builtins.py',
   'PYMODULE'),
  ('numba.cpython.charseq',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cpython\\charseq.py',
   'PYMODULE'),
  ('numba.cpython.cmathimpl',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cpython\\cmathimpl.py',
   'PYMODULE'),
  ('numba.cpython.enumimpl',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cpython\\enumimpl.py',
   'PYMODULE'),
  ('numba.cpython.hashing',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cpython\\hashing.py',
   'PYMODULE'),
  ('numba.cpython.heapq',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cpython\\heapq.py',
   'PYMODULE'),
  ('numba.cpython.iterators',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cpython\\iterators.py',
   'PYMODULE'),
  ('numba.cpython.listobj',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cpython\\listobj.py',
   'PYMODULE'),
  ('numba.cpython.mathimpl',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cpython\\mathimpl.py',
   'PYMODULE'),
  ('numba.cpython.numbers',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cpython\\numbers.py',
   'PYMODULE'),
  ('numba.cpython.printimpl',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cpython\\printimpl.py',
   'PYMODULE'),
  ('numba.cpython.randomimpl',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cpython\\randomimpl.py',
   'PYMODULE'),
  ('numba.cpython.rangeobj',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cpython\\rangeobj.py',
   'PYMODULE'),
  ('numba.cpython.setobj',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cpython\\setobj.py',
   'PYMODULE'),
  ('numba.cpython.slicing',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cpython\\slicing.py',
   'PYMODULE'),
  ('numba.cpython.tupleobj',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cpython\\tupleobj.py',
   'PYMODULE'),
  ('numba.cpython.unicode',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cpython\\unicode.py',
   'PYMODULE'),
  ('numba.cpython.unicode_support',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cpython\\unicode_support.py',
   'PYMODULE'),
  ('numba.cpython.unsafe',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cpython\\unsafe\\__init__.py',
   'PYMODULE'),
  ('numba.cpython.unsafe.numbers',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cpython\\unsafe\\numbers.py',
   'PYMODULE'),
  ('numba.cpython.unsafe.tuple',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cpython\\unsafe\\tuple.py',
   'PYMODULE'),
  ('numba.cuda',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\__init__.py',
   'PYMODULE'),
  ('numba.cuda.api',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\api.py',
   'PYMODULE'),
  ('numba.cuda.api_util',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\api_util.py',
   'PYMODULE'),
  ('numba.cuda.args',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\args.py',
   'PYMODULE'),
  ('numba.cuda.codegen',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\codegen.py',
   'PYMODULE'),
  ('numba.cuda.compiler',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\compiler.py',
   'PYMODULE'),
  ('numba.cuda.cuda_paths',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\cuda_paths.py',
   'PYMODULE'),
  ('numba.cuda.cudadecl',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\cudadecl.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\cudadrv\\__init__.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.devicearray',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\cudadrv\\devicearray.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.devices',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\cudadrv\\devices.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.driver',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\cudadrv\\driver.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.drvapi',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\cudadrv\\drvapi.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.enums',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\cudadrv\\enums.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.error',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\cudadrv\\error.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.libs',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\cudadrv\\libs.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.nvvm',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\cudadrv\\nvvm.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.rtapi',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\cudadrv\\rtapi.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.runtime',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\cudadrv\\runtime.py',
   'PYMODULE'),
  ('numba.cuda.cudaimpl',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\cudaimpl.py',
   'PYMODULE'),
  ('numba.cuda.cudamath',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\cudamath.py',
   'PYMODULE'),
  ('numba.cuda.decorators',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\decorators.py',
   'PYMODULE'),
  ('numba.cuda.descriptor',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\descriptor.py',
   'PYMODULE'),
  ('numba.cuda.device_init',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\device_init.py',
   'PYMODULE'),
  ('numba.cuda.dispatcher',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\dispatcher.py',
   'PYMODULE'),
  ('numba.cuda.errors',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\errors.py',
   'PYMODULE'),
  ('numba.cuda.extending',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\extending.py',
   'PYMODULE'),
  ('numba.cuda.initialize',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\initialize.py',
   'PYMODULE'),
  ('numba.cuda.intrinsic_wrapper',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\intrinsic_wrapper.py',
   'PYMODULE'),
  ('numba.cuda.intrinsics',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\intrinsics.py',
   'PYMODULE'),
  ('numba.cuda.kernels',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\kernels\\__init__.py',
   'PYMODULE'),
  ('numba.cuda.kernels.reduction',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\kernels\\reduction.py',
   'PYMODULE'),
  ('numba.cuda.kernels.transpose',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\kernels\\transpose.py',
   'PYMODULE'),
  ('numba.cuda.libdevice',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\libdevice.py',
   'PYMODULE'),
  ('numba.cuda.libdevicedecl',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\libdevicedecl.py',
   'PYMODULE'),
  ('numba.cuda.libdevicefuncs',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\libdevicefuncs.py',
   'PYMODULE'),
  ('numba.cuda.libdeviceimpl',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\libdeviceimpl.py',
   'PYMODULE'),
  ('numba.cuda.mathimpl',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\mathimpl.py',
   'PYMODULE'),
  ('numba.cuda.models',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\models.py',
   'PYMODULE'),
  ('numba.cuda.nvvmutils',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\nvvmutils.py',
   'PYMODULE'),
  ('numba.cuda.printimpl',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\printimpl.py',
   'PYMODULE'),
  ('numba.cuda.simulator',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\simulator\\__init__.py',
   'PYMODULE'),
  ('numba.cuda.simulator.api',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\simulator\\api.py',
   'PYMODULE'),
  ('numba.cuda.simulator.compiler',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\simulator\\compiler.py',
   'PYMODULE'),
  ('numba.cuda.simulator.cudadrv',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\simulator\\cudadrv\\__init__.py',
   'PYMODULE'),
  ('numba.cuda.simulator.cudadrv.devicearray',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\simulator\\cudadrv\\devicearray.py',
   'PYMODULE'),
  ('numba.cuda.simulator.cudadrv.devices',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\simulator\\cudadrv\\devices.py',
   'PYMODULE'),
  ('numba.cuda.simulator.cudadrv.driver',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\simulator\\cudadrv\\driver.py',
   'PYMODULE'),
  ('numba.cuda.simulator.cudadrv.drvapi',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\simulator\\cudadrv\\drvapi.py',
   'PYMODULE'),
  ('numba.cuda.simulator.cudadrv.error',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\simulator\\cudadrv\\error.py',
   'PYMODULE'),
  ('numba.cuda.simulator.cudadrv.nvvm',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\simulator\\cudadrv\\nvvm.py',
   'PYMODULE'),
  ('numba.cuda.simulator.cudadrv.runtime',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\simulator\\cudadrv\\runtime.py',
   'PYMODULE'),
  ('numba.cuda.simulator.kernel',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\simulator\\kernel.py',
   'PYMODULE'),
  ('numba.cuda.simulator.kernelapi',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\simulator\\kernelapi.py',
   'PYMODULE'),
  ('numba.cuda.simulator.reduction',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\simulator\\reduction.py',
   'PYMODULE'),
  ('numba.cuda.simulator.vector_types',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\simulator\\vector_types.py',
   'PYMODULE'),
  ('numba.cuda.simulator_init',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\simulator_init.py',
   'PYMODULE'),
  ('numba.cuda.stubs',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\stubs.py',
   'PYMODULE'),
  ('numba.cuda.target',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\target.py',
   'PYMODULE'),
  ('numba.cuda.testing',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\testing.py',
   'PYMODULE'),
  ('numba.cuda.types',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\types.py',
   'PYMODULE'),
  ('numba.cuda.ufuncs',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\ufuncs.py',
   'PYMODULE'),
  ('numba.cuda.vector_types',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\vector_types.py',
   'PYMODULE'),
  ('numba.cuda.vectorizers',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\cuda\\vectorizers.py',
   'PYMODULE'),
  ('numba.experimental',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\experimental\\__init__.py',
   'PYMODULE'),
  ('numba.experimental.function_type',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\experimental\\function_type.py',
   'PYMODULE'),
  ('numba.experimental.jitclass',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\experimental\\jitclass\\__init__.py',
   'PYMODULE'),
  ('numba.experimental.jitclass.base',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\experimental\\jitclass\\base.py',
   'PYMODULE'),
  ('numba.experimental.jitclass.boxing',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\experimental\\jitclass\\boxing.py',
   'PYMODULE'),
  ('numba.experimental.jitclass.decorators',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\experimental\\jitclass\\decorators.py',
   'PYMODULE'),
  ('numba.experimental.jitclass.overloads',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\experimental\\jitclass\\overloads.py',
   'PYMODULE'),
  ('numba.extending',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\extending.py',
   'PYMODULE'),
  ('numba.misc',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\misc\\__init__.py',
   'PYMODULE'),
  ('numba.misc.appdirs',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\misc\\appdirs.py',
   'PYMODULE'),
  ('numba.misc.cffiimpl',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\misc\\cffiimpl.py',
   'PYMODULE'),
  ('numba.misc.dummyarray',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\misc\\dummyarray.py',
   'PYMODULE'),
  ('numba.misc.dump_style',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\misc\\dump_style.py',
   'PYMODULE'),
  ('numba.misc.findlib',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\misc\\findlib.py',
   'PYMODULE'),
  ('numba.misc.firstlinefinder',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\misc\\firstlinefinder.py',
   'PYMODULE'),
  ('numba.misc.gdb_hook',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\misc\\gdb_hook.py',
   'PYMODULE'),
  ('numba.misc.init_utils',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\misc\\init_utils.py',
   'PYMODULE'),
  ('numba.misc.inspection',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\misc\\inspection.py',
   'PYMODULE'),
  ('numba.misc.literal',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\misc\\literal.py',
   'PYMODULE'),
  ('numba.misc.llvm_pass_timings',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\misc\\llvm_pass_timings.py',
   'PYMODULE'),
  ('numba.misc.mergesort',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\misc\\mergesort.py',
   'PYMODULE'),
  ('numba.misc.quicksort',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\misc\\quicksort.py',
   'PYMODULE'),
  ('numba.misc.special',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\misc\\special.py',
   'PYMODULE'),
  ('numba.np',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\np\\__init__.py',
   'PYMODULE'),
  ('numba.np.arraymath',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\np\\arraymath.py',
   'PYMODULE'),
  ('numba.np.arrayobj',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\np\\arrayobj.py',
   'PYMODULE'),
  ('numba.np.linalg',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\np\\linalg.py',
   'PYMODULE'),
  ('numba.np.npdatetime',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\np\\npdatetime.py',
   'PYMODULE'),
  ('numba.np.npdatetime_helpers',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\np\\npdatetime_helpers.py',
   'PYMODULE'),
  ('numba.np.npyfuncs',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\np\\npyfuncs.py',
   'PYMODULE'),
  ('numba.np.npyimpl',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\np\\npyimpl.py',
   'PYMODULE'),
  ('numba.np.numpy_support',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\np\\numpy_support.py',
   'PYMODULE'),
  ('numba.np.polynomial',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\np\\polynomial.py',
   'PYMODULE'),
  ('numba.np.random',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\np\\random\\__init__.py',
   'PYMODULE'),
  ('numba.np.random._constants',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\np\\random\\_constants.py',
   'PYMODULE'),
  ('numba.np.random.distributions',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\np\\random\\distributions.py',
   'PYMODULE'),
  ('numba.np.random.generator_core',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\np\\random\\generator_core.py',
   'PYMODULE'),
  ('numba.np.random.generator_methods',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\np\\random\\generator_methods.py',
   'PYMODULE'),
  ('numba.np.random.random_methods',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\np\\random\\random_methods.py',
   'PYMODULE'),
  ('numba.np.ufunc',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\np\\ufunc\\__init__.py',
   'PYMODULE'),
  ('numba.np.ufunc.array_exprs',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\np\\ufunc\\array_exprs.py',
   'PYMODULE'),
  ('numba.np.ufunc.decorators',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\np\\ufunc\\decorators.py',
   'PYMODULE'),
  ('numba.np.ufunc.deviceufunc',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\np\\ufunc\\deviceufunc.py',
   'PYMODULE'),
  ('numba.np.ufunc.dufunc',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\np\\ufunc\\dufunc.py',
   'PYMODULE'),
  ('numba.np.ufunc.gufunc',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\np\\ufunc\\gufunc.py',
   'PYMODULE'),
  ('numba.np.ufunc.parallel',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\np\\ufunc\\parallel.py',
   'PYMODULE'),
  ('numba.np.ufunc.sigparse',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\np\\ufunc\\sigparse.py',
   'PYMODULE'),
  ('numba.np.ufunc.ufuncbuilder',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\np\\ufunc\\ufuncbuilder.py',
   'PYMODULE'),
  ('numba.np.ufunc.wrappers',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\np\\ufunc\\wrappers.py',
   'PYMODULE'),
  ('numba.np.ufunc_db',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\np\\ufunc_db.py',
   'PYMODULE'),
  ('numba.np.unsafe',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\np\\unsafe\\__init__.py',
   'PYMODULE'),
  ('numba.np.unsafe.ndarray',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\np\\unsafe\\ndarray.py',
   'PYMODULE'),
  ('numba.parfors',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\parfors\\__init__.py',
   'PYMODULE'),
  ('numba.parfors.array_analysis',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\parfors\\array_analysis.py',
   'PYMODULE'),
  ('numba.parfors.parfor',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\parfors\\parfor.py',
   'PYMODULE'),
  ('numba.parfors.parfor_lowering',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\parfors\\parfor_lowering.py',
   'PYMODULE'),
  ('numba.parfors.parfor_lowering_utils',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\parfors\\parfor_lowering_utils.py',
   'PYMODULE'),
  ('numba.pycc',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\pycc\\__init__.py',
   'PYMODULE'),
  ('numba.pycc.cc',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\pycc\\cc.py',
   'PYMODULE'),
  ('numba.pycc.compiler',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\pycc\\compiler.py',
   'PYMODULE'),
  ('numba.pycc.decorators',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\pycc\\decorators.py',
   'PYMODULE'),
  ('numba.pycc.llvm_types',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\pycc\\llvm_types.py',
   'PYMODULE'),
  ('numba.pycc.platform',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\pycc\\platform.py',
   'PYMODULE'),
  ('numba.runtests',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\runtests.py',
   'PYMODULE'),
  ('numba.stencils',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\stencils\\__init__.py',
   'PYMODULE'),
  ('numba.stencils.stencil',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\stencils\\stencil.py',
   'PYMODULE'),
  ('numba.stencils.stencilparfor',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\stencils\\stencilparfor.py',
   'PYMODULE'),
  ('numba.testing',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\testing\\__init__.py',
   'PYMODULE'),
  ('numba.testing._runtests',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\testing\\_runtests.py',
   'PYMODULE'),
  ('numba.testing.loader',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\testing\\loader.py',
   'PYMODULE'),
  ('numba.testing.main',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\testing\\main.py',
   'PYMODULE'),
  ('numba.tests',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\tests\\__init__.py',
   'PYMODULE'),
  ('numba.tests.support',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\tests\\support.py',
   'PYMODULE'),
  ('numba.typed',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\typed\\__init__.py',
   'PYMODULE'),
  ('numba.typed.dictimpl',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\typed\\dictimpl.py',
   'PYMODULE'),
  ('numba.typed.dictobject',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\typed\\dictobject.py',
   'PYMODULE'),
  ('numba.typed.listobject',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\typed\\listobject.py',
   'PYMODULE'),
  ('numba.typed.typeddict',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\typed\\typeddict.py',
   'PYMODULE'),
  ('numba.typed.typedlist',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\typed\\typedlist.py',
   'PYMODULE'),
  ('numba.typed.typedobjectutils',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\typed\\typedobjectutils.py',
   'PYMODULE'),
  ('numba.types',
   'D:\\Anaconda\\Lib\\site-packages\\numba\\types\\__init__.py',
   'PYMODULE'),
  ('numbers', 'D:\\Anaconda\\Lib\\numbers.py', 'PYMODULE'),
  ('numexpr',
   'D:\\Anaconda\\Lib\\site-packages\\numexpr\\__init__.py',
   'PYMODULE'),
  ('numexpr.cpuinfo',
   'D:\\Anaconda\\Lib\\site-packages\\numexpr\\cpuinfo.py',
   'PYMODULE'),
  ('numexpr.expressions',
   'D:\\Anaconda\\Lib\\site-packages\\numexpr\\expressions.py',
   'PYMODULE'),
  ('numexpr.necompiler',
   'D:\\Anaconda\\Lib\\site-packages\\numexpr\\necompiler.py',
   'PYMODULE'),
  ('numexpr.tests',
   'D:\\Anaconda\\Lib\\site-packages\\numexpr\\tests\\__init__.py',
   'PYMODULE'),
  ('numexpr.tests.test_numexpr',
   'D:\\Anaconda\\Lib\\site-packages\\numexpr\\tests\\test_numexpr.py',
   'PYMODULE'),
  ('numexpr.utils',
   'D:\\Anaconda\\Lib\\site-packages\\numexpr\\utils.py',
   'PYMODULE'),
  ('numexpr.version',
   'D:\\Anaconda\\Lib\\site-packages\\numexpr\\version.py',
   'PYMODULE'),
  ('numpy', 'D:\\Anaconda\\Lib\\site-packages\\numpy\\__init__.py', 'PYMODULE'),
  ('numpy.__config__',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._globals',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._generic_alias',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\_typing\\_generic_alias.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._version',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\_version.py',
   'PYMODULE'),
  ('numpy.array_api',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\array_api\\__init__.py',
   'PYMODULE'),
  ('numpy.array_api._array_object',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\array_api\\_array_object.py',
   'PYMODULE'),
  ('numpy.array_api._constants',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\array_api\\_constants.py',
   'PYMODULE'),
  ('numpy.array_api._creation_functions',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\array_api\\_creation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._data_type_functions',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\array_api\\_data_type_functions.py',
   'PYMODULE'),
  ('numpy.array_api._dtypes',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\array_api\\_dtypes.py',
   'PYMODULE'),
  ('numpy.array_api._elementwise_functions',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\array_api\\_elementwise_functions.py',
   'PYMODULE'),
  ('numpy.array_api._manipulation_functions',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\array_api\\_manipulation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._searching_functions',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\array_api\\_searching_functions.py',
   'PYMODULE'),
  ('numpy.array_api._set_functions',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\array_api\\_set_functions.py',
   'PYMODULE'),
  ('numpy.array_api._sorting_functions',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\array_api\\_sorting_functions.py',
   'PYMODULE'),
  ('numpy.array_api._statistical_functions',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\array_api\\_statistical_functions.py',
   'PYMODULE'),
  ('numpy.array_api._typing',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\array_api\\_typing.py',
   'PYMODULE'),
  ('numpy.array_api._utility_functions',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\array_api\\_utility_functions.py',
   'PYMODULE'),
  ('numpy.array_api.linalg',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\array_api\\linalg.py',
   'PYMODULE'),
  ('numpy.compat',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.compat._inspect',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\compat\\_inspect.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy.core',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.core._machar',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\core\\_machar.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy.core.records',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.fft',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('numpy.linalg',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.testing',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.decorators',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\testing\\_private\\decorators.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.noseclasses',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\testing\\_private\\noseclasses.py',
   'PYMODULE'),
  ('numpy.testing._private.nosetester',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\testing\\_private\\nosetester.py',
   'PYMODULE'),
  ('numpy.testing._private.parameterized',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\testing\\_private\\parameterized.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.typing',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'D:\\Anaconda\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode', 'D:\\Anaconda\\Lib\\opcode.py', 'PYMODULE'),
  ('openpyxl',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'D:\\Anaconda\\Lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('optparse', 'D:\\Anaconda\\Lib\\optparse.py', 'PYMODULE'),
  ('packaging',
   'D:\\Anaconda\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'D:\\Anaconda\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'D:\\Anaconda\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'D:\\Anaconda\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'D:\\Anaconda\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'D:\\Anaconda\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'D:\\Anaconda\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.markers',
   'D:\\Anaconda\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.requirements',
   'D:\\Anaconda\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'D:\\Anaconda\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'D:\\Anaconda\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'D:\\Anaconda\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'D:\\Anaconda\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pandas',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE'),
  ('pandas._config',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE'),
  ('pandas._config.config',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE'),
  ('pandas._config.dates',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE'),
  ('pandas._config.display',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE'),
  ('pandas._config.localization',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE'),
  ('pandas._libs',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.window',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE'),
  ('pandas._testing',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE'),
  ('pandas._testing._io',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE'),
  ('pandas._testing._random',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_testing\\_random.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE'),
  ('pandas._typing',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE'),
  ('pandas._version',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\_version.py',
   'PYMODULE'),
  ('pandas.api',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.api.types',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.compat',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE'),
  ('pandas.compat.chainmap',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\compat\\chainmap.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE'),
  ('pandas.core',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE'),
  ('pandas.core.api',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE'),
  ('pandas.core.apply',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.dtype',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\dtype.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.dtype',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\dtype.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.base',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE'),
  ('pandas.core.common',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE'),
  ('pandas.core.computation',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE'),
  ('pandas.core.construction',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE'),
  ('pandas.core.describe',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\describe.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE'),
  ('pandas.core.flags',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE'),
  ('pandas.core.frame',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE'),
  ('pandas.core.generic',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE'),
  ('pandas.core.indexes.numeric',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\indexes\\numeric.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE'),
  ('pandas.core.internals',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE'),
  ('pandas.core.missing',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE'),
  ('pandas.core.ops',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.methods',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\ops\\methods.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE'),
  ('pandas.core.resample',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE'),
  ('pandas.core.sample',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE'),
  ('pandas.core.series',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE'),
  ('pandas.core.strings',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE'),
  ('pandas.core.tools',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE'),
  ('pandas.core.util',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE'),
  ('pandas.errors',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE'),
  ('pandas.io',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.io.api',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE'),
  ('pandas.io.common',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE'),
  ('pandas.io.date_converters',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\date_converters.py',
   'PYMODULE'),
  ('pandas.io.excel',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.excel._xlwt',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\excel\\_xlwt.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE'),
  ('pandas.io.formats',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE'),
  ('pandas.io.formats.latex',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\formats\\latex.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE'),
  ('pandas.io.html',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE'),
  ('pandas.io.json',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE'),
  ('pandas.io.orc',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE'),
  ('pandas.io.sas',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE'),
  ('pandas.io.spss',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE'),
  ('pandas.io.sql',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE'),
  ('pandas.io.stata',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE'),
  ('pandas.io.xml',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE'),
  ('pandas.plotting',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.boxplot',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\boxplot.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.compat',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\compat.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.converter',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\converter.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.core',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\core.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.groupby',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\groupby.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.hist',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\hist.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.misc',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\misc.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.style',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\style.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.timeseries',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\timeseries.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.tools',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\tools.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE'),
  ('pandas.testing',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\testing.py',
   'PYMODULE'),
  ('pandas.tseries',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE'),
  ('pandas.util',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE'),
  ('pandas.util._tester',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE'),
  ('pandas.util._validators',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE'),
  ('pandas.util.testing',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\util\\testing.py',
   'PYMODULE'),
  ('pandas.util.version',
   'D:\\Anaconda\\Lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE'),
  ('pathlib', 'D:\\Anaconda\\Lib\\pathlib.py', 'PYMODULE'),
  ('pdb', 'D:\\Anaconda\\Lib\\pdb.py', 'PYMODULE'),
  ('pexpect',
   'D:\\Anaconda\\Lib\\site-packages\\pexpect\\__init__.py',
   'PYMODULE'),
  ('pexpect._async',
   'D:\\Anaconda\\Lib\\site-packages\\pexpect\\_async.py',
   'PYMODULE'),
  ('pexpect.exceptions',
   'D:\\Anaconda\\Lib\\site-packages\\pexpect\\exceptions.py',
   'PYMODULE'),
  ('pexpect.expect',
   'D:\\Anaconda\\Lib\\site-packages\\pexpect\\expect.py',
   'PYMODULE'),
  ('pexpect.pty_spawn',
   'D:\\Anaconda\\Lib\\site-packages\\pexpect\\pty_spawn.py',
   'PYMODULE'),
  ('pexpect.run',
   'D:\\Anaconda\\Lib\\site-packages\\pexpect\\run.py',
   'PYMODULE'),
  ('pexpect.spawnbase',
   'D:\\Anaconda\\Lib\\site-packages\\pexpect\\spawnbase.py',
   'PYMODULE'),
  ('pexpect.utils',
   'D:\\Anaconda\\Lib\\site-packages\\pexpect\\utils.py',
   'PYMODULE'),
  ('pickle', 'D:\\Anaconda\\Lib\\pickle.py', 'PYMODULE'),
  ('pickletools', 'D:\\Anaconda\\Lib\\pickletools.py', 'PYMODULE'),
  ('pkg_resources',
   'D:\\Anaconda\\Lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor',
   'D:\\Anaconda\\Lib\\site-packages\\pkg_resources\\_vendor\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources',
   'D:\\Anaconda\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._adapters',
   'D:\\Anaconda\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._common',
   'D:\\Anaconda\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._compat',
   'D:\\Anaconda\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._itertools',
   'D:\\Anaconda\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._legacy',
   'D:\\Anaconda\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.abc',
   'D:\\Anaconda\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.readers',
   'D:\\Anaconda\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.simple',
   'D:\\Anaconda\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco',
   'D:\\Anaconda\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.context',
   'D:\\Anaconda\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.functools',
   'D:\\Anaconda\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.text',
   'D:\\Anaconda\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools',
   'D:\\Anaconda\\Lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.more',
   'D:\\Anaconda\\Lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.recipes',
   'D:\\Anaconda\\Lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging',
   'D:\\Anaconda\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._elffile',
   'D:\\Anaconda\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._manylinux',
   'D:\\Anaconda\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._musllinux',
   'D:\\Anaconda\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._parser',
   'D:\\Anaconda\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._structures',
   'D:\\Anaconda\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._tokenizer',
   'D:\\Anaconda\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.markers',
   'D:\\Anaconda\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.requirements',
   'D:\\Anaconda\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.specifiers',
   'D:\\Anaconda\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.tags',
   'D:\\Anaconda\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.utils',
   'D:\\Anaconda\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.version',
   'D:\\Anaconda\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs',
   'D:\\Anaconda\\Lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.__main__',
   'D:\\Anaconda\\Lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\__main__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.android',
   'D:\\Anaconda\\Lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\android.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.api',
   'D:\\Anaconda\\Lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\api.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.macos',
   'D:\\Anaconda\\Lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\macos.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.unix',
   'D:\\Anaconda\\Lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\unix.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.version',
   'D:\\Anaconda\\Lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.windows',
   'D:\\Anaconda\\Lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\windows.py',
   'PYMODULE'),
  ('pkg_resources._vendor.typing_extensions',
   'D:\\Anaconda\\Lib\\site-packages\\pkg_resources\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.zipp',
   'D:\\Anaconda\\Lib\\site-packages\\pkg_resources\\_vendor\\zipp.py',
   'PYMODULE'),
  ('pkg_resources.extern',
   'D:\\Anaconda\\Lib\\site-packages\\pkg_resources\\extern\\__init__.py',
   'PYMODULE'),
  ('pkgutil', 'D:\\Anaconda\\Lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'D:\\Anaconda\\Lib\\platform.py', 'PYMODULE'),
  ('platformdirs',
   'D:\\Anaconda\\Lib\\site-packages\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('platformdirs.android',
   'D:\\Anaconda\\Lib\\site-packages\\platformdirs\\android.py',
   'PYMODULE'),
  ('platformdirs.api',
   'D:\\Anaconda\\Lib\\site-packages\\platformdirs\\api.py',
   'PYMODULE'),
  ('platformdirs.macos',
   'D:\\Anaconda\\Lib\\site-packages\\platformdirs\\macos.py',
   'PYMODULE'),
  ('platformdirs.unix',
   'D:\\Anaconda\\Lib\\site-packages\\platformdirs\\unix.py',
   'PYMODULE'),
  ('platformdirs.version',
   'D:\\Anaconda\\Lib\\site-packages\\platformdirs\\version.py',
   'PYMODULE'),
  ('platformdirs.windows',
   'D:\\Anaconda\\Lib\\site-packages\\platformdirs\\windows.py',
   'PYMODULE'),
  ('plistlib', 'D:\\Anaconda\\Lib\\plistlib.py', 'PYMODULE'),
  ('pluggy',
   'D:\\Anaconda\\Lib\\site-packages\\pluggy\\__init__.py',
   'PYMODULE'),
  ('pluggy._callers',
   'D:\\Anaconda\\Lib\\site-packages\\pluggy\\_callers.py',
   'PYMODULE'),
  ('pluggy._hooks',
   'D:\\Anaconda\\Lib\\site-packages\\pluggy\\_hooks.py',
   'PYMODULE'),
  ('pluggy._manager',
   'D:\\Anaconda\\Lib\\site-packages\\pluggy\\_manager.py',
   'PYMODULE'),
  ('pluggy._result',
   'D:\\Anaconda\\Lib\\site-packages\\pluggy\\_result.py',
   'PYMODULE'),
  ('pluggy._tracing',
   'D:\\Anaconda\\Lib\\site-packages\\pluggy\\_tracing.py',
   'PYMODULE'),
  ('pluggy._version',
   'D:\\Anaconda\\Lib\\site-packages\\pluggy\\_version.py',
   'PYMODULE'),
  ('pprint', 'D:\\Anaconda\\Lib\\pprint.py', 'PYMODULE'),
  ('profile', 'D:\\Anaconda\\Lib\\profile.py', 'PYMODULE'),
  ('pstats', 'D:\\Anaconda\\Lib\\pstats.py', 'PYMODULE'),
  ('psutil',
   'D:\\Anaconda\\Lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._common',
   'D:\\Anaconda\\Lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('psutil._compat',
   'D:\\Anaconda\\Lib\\site-packages\\psutil\\_compat.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'D:\\Anaconda\\Lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('pty', 'D:\\Anaconda\\Lib\\pty.py', 'PYMODULE'),
  ('ptyprocess',
   'D:\\Anaconda\\Lib\\site-packages\\ptyprocess\\__init__.py',
   'PYMODULE'),
  ('ptyprocess._fork_pty',
   'D:\\Anaconda\\Lib\\site-packages\\ptyprocess\\_fork_pty.py',
   'PYMODULE'),
  ('ptyprocess.ptyprocess',
   'D:\\Anaconda\\Lib\\site-packages\\ptyprocess\\ptyprocess.py',
   'PYMODULE'),
  ('ptyprocess.util',
   'D:\\Anaconda\\Lib\\site-packages\\ptyprocess\\util.py',
   'PYMODULE'),
  ('py', 'D:\\Anaconda\\Lib\\site-packages\\py.py', 'PYMODULE'),
  ('py_compile', 'D:\\Anaconda\\Lib\\py_compile.py', 'PYMODULE'),
  ('pyarrow',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\__init__.py',
   'PYMODULE'),
  ('pyarrow._compute_docstrings',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\_compute_docstrings.py',
   'PYMODULE'),
  ('pyarrow._generated_version',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\_generated_version.py',
   'PYMODULE'),
  ('pyarrow.benchmark',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\benchmark.py',
   'PYMODULE'),
  ('pyarrow.cffi',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\cffi.py',
   'PYMODULE'),
  ('pyarrow.compute',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\compute.py',
   'PYMODULE'),
  ('pyarrow.conftest',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\conftest.py',
   'PYMODULE'),
  ('pyarrow.csv',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\csv.py',
   'PYMODULE'),
  ('pyarrow.cuda',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\cuda.py',
   'PYMODULE'),
  ('pyarrow.dataset',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\dataset.py',
   'PYMODULE'),
  ('pyarrow.feather',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\feather.py',
   'PYMODULE'),
  ('pyarrow.filesystem',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\filesystem.py',
   'PYMODULE'),
  ('pyarrow.flight',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\flight.py',
   'PYMODULE'),
  ('pyarrow.fs',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\fs.py',
   'PYMODULE'),
  ('pyarrow.hdfs',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\hdfs.py',
   'PYMODULE'),
  ('pyarrow.interchange',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\interchange\\__init__.py',
   'PYMODULE'),
  ('pyarrow.interchange.buffer',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\interchange\\buffer.py',
   'PYMODULE'),
  ('pyarrow.interchange.column',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\interchange\\column.py',
   'PYMODULE'),
  ('pyarrow.interchange.dataframe',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\interchange\\dataframe.py',
   'PYMODULE'),
  ('pyarrow.interchange.from_dataframe',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\interchange\\from_dataframe.py',
   'PYMODULE'),
  ('pyarrow.ipc',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\ipc.py',
   'PYMODULE'),
  ('pyarrow.json',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\json.py',
   'PYMODULE'),
  ('pyarrow.jvm',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\jvm.py',
   'PYMODULE'),
  ('pyarrow.orc',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\orc.py',
   'PYMODULE'),
  ('pyarrow.pandas_compat',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\pandas_compat.py',
   'PYMODULE'),
  ('pyarrow.parquet',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\parquet\\__init__.py',
   'PYMODULE'),
  ('pyarrow.parquet.core',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\parquet\\core.py',
   'PYMODULE'),
  ('pyarrow.parquet.encryption',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\parquet\\encryption.py',
   'PYMODULE'),
  ('pyarrow.plasma',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\plasma.py',
   'PYMODULE'),
  ('pyarrow.serialization',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\serialization.py',
   'PYMODULE'),
  ('pyarrow.substrait',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\substrait.py',
   'PYMODULE'),
  ('pyarrow.types',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\types.py',
   'PYMODULE'),
  ('pyarrow.util',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\util.py',
   'PYMODULE'),
  ('pyarrow.vendored',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\vendored\\__init__.py',
   'PYMODULE'),
  ('pyarrow.vendored.docscrape',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\vendored\\docscrape.py',
   'PYMODULE'),
  ('pyarrow.vendored.version',
   'D:\\Anaconda\\Lib\\site-packages\\pyarrow\\vendored\\version.py',
   'PYMODULE'),
  ('pyasn1',
   'D:\\Anaconda\\Lib\\site-packages\\pyasn1\\__init__.py',
   'PYMODULE'),
  ('pyasn1.codec',
   'D:\\Anaconda\\Lib\\site-packages\\pyasn1\\codec\\__init__.py',
   'PYMODULE'),
  ('pyasn1.codec.ber',
   'D:\\Anaconda\\Lib\\site-packages\\pyasn1\\codec\\ber\\__init__.py',
   'PYMODULE'),
  ('pyasn1.codec.ber.decoder',
   'D:\\Anaconda\\Lib\\site-packages\\pyasn1\\codec\\ber\\decoder.py',
   'PYMODULE'),
  ('pyasn1.codec.ber.eoo',
   'D:\\Anaconda\\Lib\\site-packages\\pyasn1\\codec\\ber\\eoo.py',
   'PYMODULE'),
  ('pyasn1.codec.cer',
   'D:\\Anaconda\\Lib\\site-packages\\pyasn1\\codec\\cer\\__init__.py',
   'PYMODULE'),
  ('pyasn1.codec.cer.decoder',
   'D:\\Anaconda\\Lib\\site-packages\\pyasn1\\codec\\cer\\decoder.py',
   'PYMODULE'),
  ('pyasn1.codec.der',
   'D:\\Anaconda\\Lib\\site-packages\\pyasn1\\codec\\der\\__init__.py',
   'PYMODULE'),
  ('pyasn1.codec.der.decoder',
   'D:\\Anaconda\\Lib\\site-packages\\pyasn1\\codec\\der\\decoder.py',
   'PYMODULE'),
  ('pyasn1.compat',
   'D:\\Anaconda\\Lib\\site-packages\\pyasn1\\compat\\__init__.py',
   'PYMODULE'),
  ('pyasn1.compat.binary',
   'D:\\Anaconda\\Lib\\site-packages\\pyasn1\\compat\\binary.py',
   'PYMODULE'),
  ('pyasn1.compat.calling',
   'D:\\Anaconda\\Lib\\site-packages\\pyasn1\\compat\\calling.py',
   'PYMODULE'),
  ('pyasn1.compat.dateandtime',
   'D:\\Anaconda\\Lib\\site-packages\\pyasn1\\compat\\dateandtime.py',
   'PYMODULE'),
  ('pyasn1.compat.integer',
   'D:\\Anaconda\\Lib\\site-packages\\pyasn1\\compat\\integer.py',
   'PYMODULE'),
  ('pyasn1.compat.octets',
   'D:\\Anaconda\\Lib\\site-packages\\pyasn1\\compat\\octets.py',
   'PYMODULE'),
  ('pyasn1.compat.string',
   'D:\\Anaconda\\Lib\\site-packages\\pyasn1\\compat\\string.py',
   'PYMODULE'),
  ('pyasn1.debug',
   'D:\\Anaconda\\Lib\\site-packages\\pyasn1\\debug.py',
   'PYMODULE'),
  ('pyasn1.error',
   'D:\\Anaconda\\Lib\\site-packages\\pyasn1\\error.py',
   'PYMODULE'),
  ('pyasn1.type',
   'D:\\Anaconda\\Lib\\site-packages\\pyasn1\\type\\__init__.py',
   'PYMODULE'),
  ('pyasn1.type.base',
   'D:\\Anaconda\\Lib\\site-packages\\pyasn1\\type\\base.py',
   'PYMODULE'),
  ('pyasn1.type.char',
   'D:\\Anaconda\\Lib\\site-packages\\pyasn1\\type\\char.py',
   'PYMODULE'),
  ('pyasn1.type.constraint',
   'D:\\Anaconda\\Lib\\site-packages\\pyasn1\\type\\constraint.py',
   'PYMODULE'),
  ('pyasn1.type.error',
   'D:\\Anaconda\\Lib\\site-packages\\pyasn1\\type\\error.py',
   'PYMODULE'),
  ('pyasn1.type.namedtype',
   'D:\\Anaconda\\Lib\\site-packages\\pyasn1\\type\\namedtype.py',
   'PYMODULE'),
  ('pyasn1.type.namedval',
   'D:\\Anaconda\\Lib\\site-packages\\pyasn1\\type\\namedval.py',
   'PYMODULE'),
  ('pyasn1.type.opentype',
   'D:\\Anaconda\\Lib\\site-packages\\pyasn1\\type\\opentype.py',
   'PYMODULE'),
  ('pyasn1.type.tag',
   'D:\\Anaconda\\Lib\\site-packages\\pyasn1\\type\\tag.py',
   'PYMODULE'),
  ('pyasn1.type.tagmap',
   'D:\\Anaconda\\Lib\\site-packages\\pyasn1\\type\\tagmap.py',
   'PYMODULE'),
  ('pyasn1.type.univ',
   'D:\\Anaconda\\Lib\\site-packages\\pyasn1\\type\\univ.py',
   'PYMODULE'),
  ('pyasn1.type.useful',
   'D:\\Anaconda\\Lib\\site-packages\\pyasn1\\type\\useful.py',
   'PYMODULE'),
  ('pyasn1_modules',
   'D:\\Anaconda\\Lib\\site-packages\\pyasn1_modules\\__init__.py',
   'PYMODULE'),
  ('pyasn1_modules.rfc2459',
   'D:\\Anaconda\\Lib\\site-packages\\pyasn1_modules\\rfc2459.py',
   'PYMODULE'),
  ('pycparser',
   'D:\\Anaconda\\Lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'D:\\Anaconda\\Lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'D:\\Anaconda\\Lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'D:\\Anaconda\\Lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'D:\\Anaconda\\Lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'D:\\Anaconda\\Lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.ply',
   'D:\\Anaconda\\Lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'D:\\Anaconda\\Lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'D:\\Anaconda\\Lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'D:\\Anaconda\\Lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'D:\\Anaconda\\Lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pydoc', 'D:\\Anaconda\\Lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data', 'D:\\Anaconda\\Lib\\pydoc_data\\__init__.py', 'PYMODULE'),
  ('pydoc_data.topics', 'D:\\Anaconda\\Lib\\pydoc_data\\topics.py', 'PYMODULE'),
  ('pyreadline3',
   'D:\\Anaconda\\Lib\\site-packages\\pyreadline3\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.clipboard',
   'D:\\Anaconda\\Lib\\site-packages\\pyreadline3\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.api',
   'D:\\Anaconda\\Lib\\site-packages\\pyreadline3\\clipboard\\api.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.get_clipboard_text_and_convert',
   'D:\\Anaconda\\Lib\\site-packages\\pyreadline3\\clipboard\\get_clipboard_text_and_convert.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.ironpython_clipboard',
   'D:\\Anaconda\\Lib\\site-packages\\pyreadline3\\clipboard\\ironpython_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.no_clipboard',
   'D:\\Anaconda\\Lib\\site-packages\\pyreadline3\\clipboard\\no_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.win32_clipboard',
   'D:\\Anaconda\\Lib\\site-packages\\pyreadline3\\clipboard\\win32_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.console',
   'D:\\Anaconda\\Lib\\site-packages\\pyreadline3\\console\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.console.ansi',
   'D:\\Anaconda\\Lib\\site-packages\\pyreadline3\\console\\ansi.py',
   'PYMODULE'),
  ('pyreadline3.console.console',
   'D:\\Anaconda\\Lib\\site-packages\\pyreadline3\\console\\console.py',
   'PYMODULE'),
  ('pyreadline3.console.event',
   'D:\\Anaconda\\Lib\\site-packages\\pyreadline3\\console\\event.py',
   'PYMODULE'),
  ('pyreadline3.console.ironpython_console',
   'D:\\Anaconda\\Lib\\site-packages\\pyreadline3\\console\\ironpython_console.py',
   'PYMODULE'),
  ('pyreadline3.error',
   'D:\\Anaconda\\Lib\\site-packages\\pyreadline3\\error.py',
   'PYMODULE'),
  ('pyreadline3.keysyms',
   'D:\\Anaconda\\Lib\\site-packages\\pyreadline3\\keysyms\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.common',
   'D:\\Anaconda\\Lib\\site-packages\\pyreadline3\\keysyms\\common.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.ironpython_keysyms',
   'D:\\Anaconda\\Lib\\site-packages\\pyreadline3\\keysyms\\ironpython_keysyms.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.keysyms',
   'D:\\Anaconda\\Lib\\site-packages\\pyreadline3\\keysyms\\keysyms.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.winconstants',
   'D:\\Anaconda\\Lib\\site-packages\\pyreadline3\\keysyms\\winconstants.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor',
   'D:\\Anaconda\\Lib\\site-packages\\pyreadline3\\lineeditor\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.history',
   'D:\\Anaconda\\Lib\\site-packages\\pyreadline3\\lineeditor\\history.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.lineobj',
   'D:\\Anaconda\\Lib\\site-packages\\pyreadline3\\lineeditor\\lineobj.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.wordmatcher',
   'D:\\Anaconda\\Lib\\site-packages\\pyreadline3\\lineeditor\\wordmatcher.py',
   'PYMODULE'),
  ('pyreadline3.logger',
   'D:\\Anaconda\\Lib\\site-packages\\pyreadline3\\logger\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.logger.control',
   'D:\\Anaconda\\Lib\\site-packages\\pyreadline3\\logger\\control.py',
   'PYMODULE'),
  ('pyreadline3.logger.log',
   'D:\\Anaconda\\Lib\\site-packages\\pyreadline3\\logger\\log.py',
   'PYMODULE'),
  ('pyreadline3.logger.logger',
   'D:\\Anaconda\\Lib\\site-packages\\pyreadline3\\logger\\logger.py',
   'PYMODULE'),
  ('pyreadline3.logger.null_handler',
   'D:\\Anaconda\\Lib\\site-packages\\pyreadline3\\logger\\null_handler.py',
   'PYMODULE'),
  ('pyreadline3.logger.socket_stream',
   'D:\\Anaconda\\Lib\\site-packages\\pyreadline3\\logger\\socket_stream.py',
   'PYMODULE'),
  ('pyreadline3.modes',
   'D:\\Anaconda\\Lib\\site-packages\\pyreadline3\\modes\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.modes.basemode',
   'D:\\Anaconda\\Lib\\site-packages\\pyreadline3\\modes\\basemode.py',
   'PYMODULE'),
  ('pyreadline3.modes.emacs',
   'D:\\Anaconda\\Lib\\site-packages\\pyreadline3\\modes\\emacs.py',
   'PYMODULE'),
  ('pyreadline3.modes.notemacs',
   'D:\\Anaconda\\Lib\\site-packages\\pyreadline3\\modes\\notemacs.py',
   'PYMODULE'),
  ('pyreadline3.modes.vi',
   'D:\\Anaconda\\Lib\\site-packages\\pyreadline3\\modes\\vi.py',
   'PYMODULE'),
  ('pyreadline3.py3k_compat',
   'D:\\Anaconda\\Lib\\site-packages\\pyreadline3\\py3k_compat.py',
   'PYMODULE'),
  ('pyreadline3.rlmain',
   'D:\\Anaconda\\Lib\\site-packages\\pyreadline3\\rlmain.py',
   'PYMODULE'),
  ('pyreadline3.unicode_helper',
   'D:\\Anaconda\\Lib\\site-packages\\pyreadline3\\unicode_helper.py',
   'PYMODULE'),
  ('pytest',
   'D:\\Anaconda\\Lib\\site-packages\\pytest\\__init__.py',
   'PYMODULE'),
  ('pytz', 'D:\\Anaconda\\Lib\\site-packages\\pytz\\__init__.py', 'PYMODULE'),
  ('pytz.exceptions',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pytz.lazy', 'D:\\Anaconda\\Lib\\site-packages\\pytz\\lazy.py', 'PYMODULE'),
  ('pytz.tzfile',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'D:\\Anaconda\\Lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('qtpy', 'D:\\Anaconda\\Lib\\site-packages\\qtpy\\__init__.py', 'PYMODULE'),
  ('qtpy.QtDataVisualization',
   'D:\\Anaconda\\Lib\\site-packages\\qtpy\\QtDataVisualization.py',
   'PYMODULE'),
  ('qtpy.QtWidgets',
   'D:\\Anaconda\\Lib\\site-packages\\qtpy\\QtWidgets.py',
   'PYMODULE'),
  ('qtpy.enums_compat',
   'D:\\Anaconda\\Lib\\site-packages\\qtpy\\enums_compat.py',
   'PYMODULE'),
  ('qtpy.sip', 'D:\\Anaconda\\Lib\\site-packages\\qtpy\\sip.py', 'PYMODULE'),
  ('queue', 'D:\\Anaconda\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'D:\\Anaconda\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\Anaconda\\Lib\\random.py', 'PYMODULE'),
  ('readline', 'D:\\Anaconda\\Lib\\site-packages\\readline.py', 'PYMODULE'),
  ('requests',
   'D:\\Anaconda\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'D:\\Anaconda\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'D:\\Anaconda\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'D:\\Anaconda\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'D:\\Anaconda\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'D:\\Anaconda\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'D:\\Anaconda\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'D:\\Anaconda\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'D:\\Anaconda\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'D:\\Anaconda\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'D:\\Anaconda\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'D:\\Anaconda\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'D:\\Anaconda\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'D:\\Anaconda\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'D:\\Anaconda\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'D:\\Anaconda\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'D:\\Anaconda\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('rlcompleter', 'D:\\Anaconda\\Lib\\rlcompleter.py', 'PYMODULE'),
  ('runpy', 'D:\\Anaconda\\Lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'D:\\Anaconda\\Lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'D:\\Anaconda\\Lib\\selectors.py', 'PYMODULE'),
  ('service_identity',
   'D:\\Anaconda\\Lib\\site-packages\\service_identity\\__init__.py',
   'PYMODULE'),
  ('service_identity._common',
   'D:\\Anaconda\\Lib\\site-packages\\service_identity\\_common.py',
   'PYMODULE'),
  ('service_identity._compat',
   'D:\\Anaconda\\Lib\\site-packages\\service_identity\\_compat.py',
   'PYMODULE'),
  ('service_identity.cryptography',
   'D:\\Anaconda\\Lib\\site-packages\\service_identity\\cryptography.py',
   'PYMODULE'),
  ('service_identity.exceptions',
   'D:\\Anaconda\\Lib\\site-packages\\service_identity\\exceptions.py',
   'PYMODULE'),
  ('service_identity.pyopenssl',
   'D:\\Anaconda\\Lib\\site-packages\\service_identity\\pyopenssl.py',
   'PYMODULE'),
  ('setuptools',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._collections',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\_collections.py',
   'PYMODULE'),
  ('setuptools._distutils._functools',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\_functools.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._macos_compat',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\_macos_compat.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.bcppcompiler',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\bcppcompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command._framework_compat',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\command\\_framework_compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_dumb',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist_dumb.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_rpm',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_clib',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_clib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_py',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_py.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_scripts',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.check',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\command\\check.py',
   'PYMODULE'),
  ('setuptools._distutils.command.clean',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\command\\clean.py',
   'PYMODULE'),
  ('setuptools._distutils.command.config',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\command\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\command\\install.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_data',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_data.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_egg_info',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_headers',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_headers.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_lib',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_lib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_scripts',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.py37compat',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\command\\py37compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.register',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\command\\register.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.upload',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\command\\upload.py',
   'PYMODULE'),
  ('setuptools._distutils.config',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.cygwinccompiler',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\cygwinccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dep_util',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\dep_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.msvc9compiler',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('setuptools._distutils.msvccompiler',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.py38compat',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\py38compat.py',
   'PYMODULE'),
  ('setuptools._distutils.py39compat',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\py39compat.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.unixccompiler',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\unixccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._vendor',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_vendor\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._py39compat',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_py39compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._adapters',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._common',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._compat',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._itertools',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._legacy',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.abc',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.readers',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.simple',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.ordered_set',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_vendor\\ordered_set.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\_vendor\\zipp.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.build_ext',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.py36compat',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\command\\py36compat.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.config',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.extern',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\extern\\__init__.py',
   'PYMODULE'),
  ('setuptools.glob',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.py34compat',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\py34compat.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'D:\\Anaconda\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shelve', 'D:\\Anaconda\\Lib\\shelve.py', 'PYMODULE'),
  ('shlex', 'D:\\Anaconda\\Lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'D:\\Anaconda\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\Anaconda\\Lib\\signal.py', 'PYMODULE'),
  ('site', 'D:\\Anaconda\\Lib\\site.py', 'PYMODULE'),
  ('six', 'D:\\Anaconda\\Lib\\site-packages\\six.py', 'PYMODULE'),
  ('smtplib', 'D:\\Anaconda\\Lib\\smtplib.py', 'PYMODULE'),
  ('snappy',
   'D:\\Anaconda\\Lib\\site-packages\\snappy\\__init__.py',
   'PYMODULE'),
  ('snappy.hadoop_snappy',
   'D:\\Anaconda\\Lib\\site-packages\\snappy\\hadoop_snappy.py',
   'PYMODULE'),
  ('snappy.snappy',
   'D:\\Anaconda\\Lib\\site-packages\\snappy\\snappy.py',
   'PYMODULE'),
  ('snappy.snappy_cffi',
   'D:\\Anaconda\\Lib\\site-packages\\snappy\\snappy_cffi.py',
   'PYMODULE'),
  ('socket', 'D:\\Anaconda\\Lib\\socket.py', 'PYMODULE'),
  ('socketserver', 'D:\\Anaconda\\Lib\\socketserver.py', 'PYMODULE'),
  ('socks', 'D:\\Anaconda\\Lib\\site-packages\\socks.py', 'PYMODULE'),
  ('soupsieve',
   'D:\\Anaconda\\Lib\\site-packages\\soupsieve\\__init__.py',
   'PYMODULE'),
  ('soupsieve.__meta__',
   'D:\\Anaconda\\Lib\\site-packages\\soupsieve\\__meta__.py',
   'PYMODULE'),
  ('soupsieve.css_match',
   'D:\\Anaconda\\Lib\\site-packages\\soupsieve\\css_match.py',
   'PYMODULE'),
  ('soupsieve.css_parser',
   'D:\\Anaconda\\Lib\\site-packages\\soupsieve\\css_parser.py',
   'PYMODULE'),
  ('soupsieve.css_types',
   'D:\\Anaconda\\Lib\\site-packages\\soupsieve\\css_types.py',
   'PYMODULE'),
  ('soupsieve.pretty',
   'D:\\Anaconda\\Lib\\site-packages\\soupsieve\\pretty.py',
   'PYMODULE'),
  ('soupsieve.util',
   'D:\\Anaconda\\Lib\\site-packages\\soupsieve\\util.py',
   'PYMODULE'),
  ('sqlite3', 'D:\\Anaconda\\Lib\\sqlite3\\__init__.py', 'PYMODULE'),
  ('sqlite3.dbapi2', 'D:\\Anaconda\\Lib\\sqlite3\\dbapi2.py', 'PYMODULE'),
  ('sqlite3.dump', 'D:\\Anaconda\\Lib\\sqlite3\\dump.py', 'PYMODULE'),
  ('ssl', 'D:\\Anaconda\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'D:\\Anaconda\\Lib\\statistics.py', 'PYMODULE'),
  ('string', 'D:\\Anaconda\\Lib\\string.py', 'PYMODULE'),
  ('stringprep', 'D:\\Anaconda\\Lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'D:\\Anaconda\\Lib\\subprocess.py', 'PYMODULE'),
  ('symtable', 'D:\\Anaconda\\Lib\\symtable.py', 'PYMODULE'),
  ('sysconfig', 'D:\\Anaconda\\Lib\\sysconfig.py', 'PYMODULE'),
  ('tarfile', 'D:\\Anaconda\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'D:\\Anaconda\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'D:\\Anaconda\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'D:\\Anaconda\\Lib\\threading.py', 'PYMODULE'),
  ('threadpoolctl',
   'D:\\Anaconda\\Lib\\site-packages\\threadpoolctl.py',
   'PYMODULE'),
  ('timeit', 'D:\\Anaconda\\Lib\\timeit.py', 'PYMODULE'),
  ('token', 'D:\\Anaconda\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\Anaconda\\Lib\\tokenize.py', 'PYMODULE'),
  ('tomli', 'D:\\Anaconda\\Lib\\site-packages\\tomli\\__init__.py', 'PYMODULE'),
  ('tomli._parser',
   'D:\\Anaconda\\Lib\\site-packages\\tomli\\_parser.py',
   'PYMODULE'),
  ('tomli._re', 'D:\\Anaconda\\Lib\\site-packages\\tomli\\_re.py', 'PYMODULE'),
  ('tomli._types',
   'D:\\Anaconda\\Lib\\site-packages\\tomli\\_types.py',
   'PYMODULE'),
  ('tomllib', 'D:\\Anaconda\\Lib\\tomllib\\__init__.py', 'PYMODULE'),
  ('tomllib._parser', 'D:\\Anaconda\\Lib\\tomllib\\_parser.py', 'PYMODULE'),
  ('tomllib._re', 'D:\\Anaconda\\Lib\\tomllib\\_re.py', 'PYMODULE'),
  ('tomllib._types', 'D:\\Anaconda\\Lib\\tomllib\\_types.py', 'PYMODULE'),
  ('tornado',
   'D:\\Anaconda\\Lib\\site-packages\\tornado\\__init__.py',
   'PYMODULE'),
  ('tornado.concurrent',
   'D:\\Anaconda\\Lib\\site-packages\\tornado\\concurrent.py',
   'PYMODULE'),
  ('tornado.escape',
   'D:\\Anaconda\\Lib\\site-packages\\tornado\\escape.py',
   'PYMODULE'),
  ('tornado.gen',
   'D:\\Anaconda\\Lib\\site-packages\\tornado\\gen.py',
   'PYMODULE'),
  ('tornado.ioloop',
   'D:\\Anaconda\\Lib\\site-packages\\tornado\\ioloop.py',
   'PYMODULE'),
  ('tornado.iostream',
   'D:\\Anaconda\\Lib\\site-packages\\tornado\\iostream.py',
   'PYMODULE'),
  ('tornado.locks',
   'D:\\Anaconda\\Lib\\site-packages\\tornado\\locks.py',
   'PYMODULE'),
  ('tornado.log',
   'D:\\Anaconda\\Lib\\site-packages\\tornado\\log.py',
   'PYMODULE'),
  ('tornado.netutil',
   'D:\\Anaconda\\Lib\\site-packages\\tornado\\netutil.py',
   'PYMODULE'),
  ('tornado.options',
   'D:\\Anaconda\\Lib\\site-packages\\tornado\\options.py',
   'PYMODULE'),
  ('tornado.platform',
   'D:\\Anaconda\\Lib\\site-packages\\tornado\\platform\\__init__.py',
   'PYMODULE'),
  ('tornado.platform.asyncio',
   'D:\\Anaconda\\Lib\\site-packages\\tornado\\platform\\asyncio.py',
   'PYMODULE'),
  ('tornado.process',
   'D:\\Anaconda\\Lib\\site-packages\\tornado\\process.py',
   'PYMODULE'),
  ('tornado.queues',
   'D:\\Anaconda\\Lib\\site-packages\\tornado\\queues.py',
   'PYMODULE'),
  ('tornado.util',
   'D:\\Anaconda\\Lib\\site-packages\\tornado\\util.py',
   'PYMODULE'),
  ('tqdm', 'D:\\Anaconda\\Lib\\site-packages\\tqdm\\__init__.py', 'PYMODULE'),
  ('tqdm._dist_ver',
   'D:\\Anaconda\\Lib\\site-packages\\tqdm\\_dist_ver.py',
   'PYMODULE'),
  ('tqdm._monitor',
   'D:\\Anaconda\\Lib\\site-packages\\tqdm\\_monitor.py',
   'PYMODULE'),
  ('tqdm._tqdm_pandas',
   'D:\\Anaconda\\Lib\\site-packages\\tqdm\\_tqdm_pandas.py',
   'PYMODULE'),
  ('tqdm.cli', 'D:\\Anaconda\\Lib\\site-packages\\tqdm\\cli.py', 'PYMODULE'),
  ('tqdm.gui', 'D:\\Anaconda\\Lib\\site-packages\\tqdm\\gui.py', 'PYMODULE'),
  ('tqdm.notebook',
   'D:\\Anaconda\\Lib\\site-packages\\tqdm\\notebook.py',
   'PYMODULE'),
  ('tqdm.std', 'D:\\Anaconda\\Lib\\site-packages\\tqdm\\std.py', 'PYMODULE'),
  ('tqdm.utils',
   'D:\\Anaconda\\Lib\\site-packages\\tqdm\\utils.py',
   'PYMODULE'),
  ('tqdm.version',
   'D:\\Anaconda\\Lib\\site-packages\\tqdm\\version.py',
   'PYMODULE'),
  ('tracemalloc', 'D:\\Anaconda\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('traitlets',
   'D:\\Anaconda\\Lib\\site-packages\\traitlets\\__init__.py',
   'PYMODULE'),
  ('traitlets._version',
   'D:\\Anaconda\\Lib\\site-packages\\traitlets\\_version.py',
   'PYMODULE'),
  ('traitlets.config',
   'D:\\Anaconda\\Lib\\site-packages\\traitlets\\config\\__init__.py',
   'PYMODULE'),
  ('traitlets.config.application',
   'D:\\Anaconda\\Lib\\site-packages\\traitlets\\config\\application.py',
   'PYMODULE'),
  ('traitlets.config.configurable',
   'D:\\Anaconda\\Lib\\site-packages\\traitlets\\config\\configurable.py',
   'PYMODULE'),
  ('traitlets.config.loader',
   'D:\\Anaconda\\Lib\\site-packages\\traitlets\\config\\loader.py',
   'PYMODULE'),
  ('traitlets.log',
   'D:\\Anaconda\\Lib\\site-packages\\traitlets\\log.py',
   'PYMODULE'),
  ('traitlets.traitlets',
   'D:\\Anaconda\\Lib\\site-packages\\traitlets\\traitlets.py',
   'PYMODULE'),
  ('traitlets.utils',
   'D:\\Anaconda\\Lib\\site-packages\\traitlets\\utils\\__init__.py',
   'PYMODULE'),
  ('traitlets.utils.bunch',
   'D:\\Anaconda\\Lib\\site-packages\\traitlets\\utils\\bunch.py',
   'PYMODULE'),
  ('traitlets.utils.decorators',
   'D:\\Anaconda\\Lib\\site-packages\\traitlets\\utils\\decorators.py',
   'PYMODULE'),
  ('traitlets.utils.descriptions',
   'D:\\Anaconda\\Lib\\site-packages\\traitlets\\utils\\descriptions.py',
   'PYMODULE'),
  ('traitlets.utils.getargspec',
   'D:\\Anaconda\\Lib\\site-packages\\traitlets\\utils\\getargspec.py',
   'PYMODULE'),
  ('traitlets.utils.importstring',
   'D:\\Anaconda\\Lib\\site-packages\\traitlets\\utils\\importstring.py',
   'PYMODULE'),
  ('traitlets.utils.nested_update',
   'D:\\Anaconda\\Lib\\site-packages\\traitlets\\utils\\nested_update.py',
   'PYMODULE'),
  ('traitlets.utils.sentinel',
   'D:\\Anaconda\\Lib\\site-packages\\traitlets\\utils\\sentinel.py',
   'PYMODULE'),
  ('traitlets.utils.text',
   'D:\\Anaconda\\Lib\\site-packages\\traitlets\\utils\\text.py',
   'PYMODULE'),
  ('tty', 'D:\\Anaconda\\Lib\\tty.py', 'PYMODULE'),
  ('twisted',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\__init__.py',
   'PYMODULE'),
  ('twisted._threads',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\_threads\\__init__.py',
   'PYMODULE'),
  ('twisted._threads._convenience',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\_threads\\_convenience.py',
   'PYMODULE'),
  ('twisted._threads._ithreads',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\_threads\\_ithreads.py',
   'PYMODULE'),
  ('twisted._threads._memory',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\_threads\\_memory.py',
   'PYMODULE'),
  ('twisted._threads._pool',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\_threads\\_pool.py',
   'PYMODULE'),
  ('twisted._threads._team',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\_threads\\_team.py',
   'PYMODULE'),
  ('twisted._threads._threadworker',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\_threads\\_threadworker.py',
   'PYMODULE'),
  ('twisted._version',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\_version.py',
   'PYMODULE'),
  ('twisted.internet',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\internet\\__init__.py',
   'PYMODULE'),
  ('twisted.internet._baseprocess',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\internet\\_baseprocess.py',
   'PYMODULE'),
  ('twisted.internet._dumbwin32proc',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\internet\\_dumbwin32proc.py',
   'PYMODULE'),
  ('twisted.internet._idna',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\internet\\_idna.py',
   'PYMODULE'),
  ('twisted.internet._newtls',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\internet\\_newtls.py',
   'PYMODULE'),
  ('twisted.internet._pollingfile',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\internet\\_pollingfile.py',
   'PYMODULE'),
  ('twisted.internet._producer_helpers',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\internet\\_producer_helpers.py',
   'PYMODULE'),
  ('twisted.internet._resolver',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\internet\\_resolver.py',
   'PYMODULE'),
  ('twisted.internet._signals',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\internet\\_signals.py',
   'PYMODULE'),
  ('twisted.internet._sslverify',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\internet\\_sslverify.py',
   'PYMODULE'),
  ('twisted.internet.abstract',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\internet\\abstract.py',
   'PYMODULE'),
  ('twisted.internet.address',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\internet\\address.py',
   'PYMODULE'),
  ('twisted.internet.base',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\internet\\base.py',
   'PYMODULE'),
  ('twisted.internet.default',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\internet\\default.py',
   'PYMODULE'),
  ('twisted.internet.defer',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\internet\\defer.py',
   'PYMODULE'),
  ('twisted.internet.epollreactor',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\internet\\epollreactor.py',
   'PYMODULE'),
  ('twisted.internet.error',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\internet\\error.py',
   'PYMODULE'),
  ('twisted.internet.fdesc',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\internet\\fdesc.py',
   'PYMODULE'),
  ('twisted.internet.interfaces',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\internet\\interfaces.py',
   'PYMODULE'),
  ('twisted.internet.main',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\internet\\main.py',
   'PYMODULE'),
  ('twisted.internet.pollreactor',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\internet\\pollreactor.py',
   'PYMODULE'),
  ('twisted.internet.posixbase',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\internet\\posixbase.py',
   'PYMODULE'),
  ('twisted.internet.process',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\internet\\process.py',
   'PYMODULE'),
  ('twisted.internet.protocol',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\internet\\protocol.py',
   'PYMODULE'),
  ('twisted.internet.reactor',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\internet\\reactor.py',
   'PYMODULE'),
  ('twisted.internet.selectreactor',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\internet\\selectreactor.py',
   'PYMODULE'),
  ('twisted.internet.ssl',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\internet\\ssl.py',
   'PYMODULE'),
  ('twisted.internet.task',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\internet\\task.py',
   'PYMODULE'),
  ('twisted.internet.tcp',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\internet\\tcp.py',
   'PYMODULE'),
  ('twisted.internet.threads',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\internet\\threads.py',
   'PYMODULE'),
  ('twisted.internet.udp',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\internet\\udp.py',
   'PYMODULE'),
  ('twisted.internet.unix',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\internet\\unix.py',
   'PYMODULE'),
  ('twisted.internet.utils',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\internet\\utils.py',
   'PYMODULE'),
  ('twisted.internet.win32eventreactor',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\internet\\win32eventreactor.py',
   'PYMODULE'),
  ('twisted.logger',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\logger\\__init__.py',
   'PYMODULE'),
  ('twisted.logger._buffer',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\logger\\_buffer.py',
   'PYMODULE'),
  ('twisted.logger._capture',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\logger\\_capture.py',
   'PYMODULE'),
  ('twisted.logger._file',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\logger\\_file.py',
   'PYMODULE'),
  ('twisted.logger._filter',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\logger\\_filter.py',
   'PYMODULE'),
  ('twisted.logger._flatten',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\logger\\_flatten.py',
   'PYMODULE'),
  ('twisted.logger._format',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\logger\\_format.py',
   'PYMODULE'),
  ('twisted.logger._global',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\logger\\_global.py',
   'PYMODULE'),
  ('twisted.logger._interfaces',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\logger\\_interfaces.py',
   'PYMODULE'),
  ('twisted.logger._io',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\logger\\_io.py',
   'PYMODULE'),
  ('twisted.logger._json',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\logger\\_json.py',
   'PYMODULE'),
  ('twisted.logger._legacy',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\logger\\_legacy.py',
   'PYMODULE'),
  ('twisted.logger._levels',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\logger\\_levels.py',
   'PYMODULE'),
  ('twisted.logger._logger',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\logger\\_logger.py',
   'PYMODULE'),
  ('twisted.logger._observer',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\logger\\_observer.py',
   'PYMODULE'),
  ('twisted.logger._stdlib',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\logger\\_stdlib.py',
   'PYMODULE'),
  ('twisted.names',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\names\\__init__.py',
   'PYMODULE'),
  ('twisted.names.dns',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\names\\dns.py',
   'PYMODULE'),
  ('twisted.names.error',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\names\\error.py',
   'PYMODULE'),
  ('twisted.protocols',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\protocols\\__init__.py',
   'PYMODULE'),
  ('twisted.protocols.policies',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\protocols\\policies.py',
   'PYMODULE'),
  ('twisted.protocols.tls',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\protocols\\tls.py',
   'PYMODULE'),
  ('twisted.python',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\python\\__init__.py',
   'PYMODULE'),
  ('twisted.python._inotify',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\python\\_inotify.py',
   'PYMODULE'),
  ('twisted.python._tzhelper',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\python\\_tzhelper.py',
   'PYMODULE'),
  ('twisted.python.compat',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\python\\compat.py',
   'PYMODULE'),
  ('twisted.python.components',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\python\\components.py',
   'PYMODULE'),
  ('twisted.python.context',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\python\\context.py',
   'PYMODULE'),
  ('twisted.python.deprecate',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\python\\deprecate.py',
   'PYMODULE'),
  ('twisted.python.failure',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\python\\failure.py',
   'PYMODULE'),
  ('twisted.python.filepath',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\python\\filepath.py',
   'PYMODULE'),
  ('twisted.python.lockfile',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\python\\lockfile.py',
   'PYMODULE'),
  ('twisted.python.log',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\python\\log.py',
   'PYMODULE'),
  ('twisted.python.monkey',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\python\\monkey.py',
   'PYMODULE'),
  ('twisted.python.randbytes',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\python\\randbytes.py',
   'PYMODULE'),
  ('twisted.python.reflect',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\python\\reflect.py',
   'PYMODULE'),
  ('twisted.python.runtime',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\python\\runtime.py',
   'PYMODULE'),
  ('twisted.python.sendmsg',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\python\\sendmsg.py',
   'PYMODULE'),
  ('twisted.python.threadable',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\python\\threadable.py',
   'PYMODULE'),
  ('twisted.python.threadpool',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\python\\threadpool.py',
   'PYMODULE'),
  ('twisted.python.util',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\python\\util.py',
   'PYMODULE'),
  ('twisted.python.versions',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\python\\versions.py',
   'PYMODULE'),
  ('twisted.python.win32',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\python\\win32.py',
   'PYMODULE'),
  ('twisted.trial',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\trial\\__init__.py',
   'PYMODULE'),
  ('twisted.trial._asyncrunner',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\trial\\_asyncrunner.py',
   'PYMODULE'),
  ('twisted.trial._asynctest',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\trial\\_asynctest.py',
   'PYMODULE'),
  ('twisted.trial._synctest',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\trial\\_synctest.py',
   'PYMODULE'),
  ('twisted.trial.itrial',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\trial\\itrial.py',
   'PYMODULE'),
  ('twisted.trial.reporter',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\trial\\reporter.py',
   'PYMODULE'),
  ('twisted.trial.unittest',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\trial\\unittest.py',
   'PYMODULE'),
  ('twisted.trial.util',
   'D:\\Anaconda\\Lib\\site-packages\\twisted\\trial\\util.py',
   'PYMODULE'),
  ('typing', 'D:\\Anaconda\\Lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'D:\\Anaconda\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('unittest', 'D:\\Anaconda\\Lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest._log', 'D:\\Anaconda\\Lib\\unittest\\_log.py', 'PYMODULE'),
  ('unittest.async_case',
   'D:\\Anaconda\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case', 'D:\\Anaconda\\Lib\\unittest\\case.py', 'PYMODULE'),
  ('unittest.loader', 'D:\\Anaconda\\Lib\\unittest\\loader.py', 'PYMODULE'),
  ('unittest.main', 'D:\\Anaconda\\Lib\\unittest\\main.py', 'PYMODULE'),
  ('unittest.mock', 'D:\\Anaconda\\Lib\\unittest\\mock.py', 'PYMODULE'),
  ('unittest.result', 'D:\\Anaconda\\Lib\\unittest\\result.py', 'PYMODULE'),
  ('unittest.runner', 'D:\\Anaconda\\Lib\\unittest\\runner.py', 'PYMODULE'),
  ('unittest.signals', 'D:\\Anaconda\\Lib\\unittest\\signals.py', 'PYMODULE'),
  ('unittest.suite', 'D:\\Anaconda\\Lib\\unittest\\suite.py', 'PYMODULE'),
  ('unittest.util', 'D:\\Anaconda\\Lib\\unittest\\util.py', 'PYMODULE'),
  ('urllib', 'D:\\Anaconda\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.error', 'D:\\Anaconda\\Lib\\urllib\\error.py', 'PYMODULE'),
  ('urllib.parse', 'D:\\Anaconda\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('urllib.request', 'D:\\Anaconda\\Lib\\urllib\\request.py', 'PYMODULE'),
  ('urllib.response', 'D:\\Anaconda\\Lib\\urllib\\response.py', 'PYMODULE'),
  ('urllib3',
   'D:\\Anaconda\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._collections',
   'D:\\Anaconda\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._version',
   'D:\\Anaconda\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'D:\\Anaconda\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'D:\\Anaconda\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'D:\\Anaconda\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib._appengine_environ',
   'D:\\Anaconda\\Lib\\site-packages\\urllib3\\contrib\\_appengine_environ.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'D:\\Anaconda\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'D:\\Anaconda\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'D:\\Anaconda\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'D:\\Anaconda\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'D:\\Anaconda\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.packages',
   'D:\\Anaconda\\Lib\\site-packages\\urllib3\\packages\\__init__.py',
   'PYMODULE'),
  ('urllib3.packages.backports',
   'D:\\Anaconda\\Lib\\site-packages\\urllib3\\packages\\backports\\__init__.py',
   'PYMODULE'),
  ('urllib3.packages.backports.makefile',
   'D:\\Anaconda\\Lib\\site-packages\\urllib3\\packages\\backports\\makefile.py',
   'PYMODULE'),
  ('urllib3.packages.backports.weakref_finalize',
   'D:\\Anaconda\\Lib\\site-packages\\urllib3\\packages\\backports\\weakref_finalize.py',
   'PYMODULE'),
  ('urllib3.packages.six',
   'D:\\Anaconda\\Lib\\site-packages\\urllib3\\packages\\six.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'D:\\Anaconda\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.request',
   'D:\\Anaconda\\Lib\\site-packages\\urllib3\\request.py',
   'PYMODULE'),
  ('urllib3.response',
   'D:\\Anaconda\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'D:\\Anaconda\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'D:\\Anaconda\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'D:\\Anaconda\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.queue',
   'D:\\Anaconda\\Lib\\site-packages\\urllib3\\util\\queue.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'D:\\Anaconda\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'D:\\Anaconda\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'D:\\Anaconda\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'D:\\Anaconda\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'D:\\Anaconda\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'D:\\Anaconda\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'D:\\Anaconda\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'D:\\Anaconda\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'D:\\Anaconda\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('uuid', 'D:\\Anaconda\\Lib\\uuid.py', 'PYMODULE'),
  ('webbrowser', 'D:\\Anaconda\\Lib\\webbrowser.py', 'PYMODULE'),
  ('win32con',
   'D:\\Anaconda\\Lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('win32evtlogutil',
   'D:\\Anaconda\\Lib\\site-packages\\win32\\lib\\win32evtlogutil.py',
   'PYMODULE'),
  ('win_inet_pton',
   'D:\\Anaconda\\Lib\\site-packages\\win_inet_pton.py',
   'PYMODULE'),
  ('winerror',
   'D:\\Anaconda\\Lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('xdrlib', 'D:\\Anaconda\\Lib\\xdrlib.py', 'PYMODULE'),
  ('xlrd', 'D:\\Anaconda\\Lib\\site-packages\\xlrd\\__init__.py', 'PYMODULE'),
  ('xlrd.biffh',
   'D:\\Anaconda\\Lib\\site-packages\\xlrd\\biffh.py',
   'PYMODULE'),
  ('xlrd.book', 'D:\\Anaconda\\Lib\\site-packages\\xlrd\\book.py', 'PYMODULE'),
  ('xlrd.compdoc',
   'D:\\Anaconda\\Lib\\site-packages\\xlrd\\compdoc.py',
   'PYMODULE'),
  ('xlrd.formatting',
   'D:\\Anaconda\\Lib\\site-packages\\xlrd\\formatting.py',
   'PYMODULE'),
  ('xlrd.formula',
   'D:\\Anaconda\\Lib\\site-packages\\xlrd\\formula.py',
   'PYMODULE'),
  ('xlrd.info', 'D:\\Anaconda\\Lib\\site-packages\\xlrd\\info.py', 'PYMODULE'),
  ('xlrd.sheet',
   'D:\\Anaconda\\Lib\\site-packages\\xlrd\\sheet.py',
   'PYMODULE'),
  ('xlrd.timemachine',
   'D:\\Anaconda\\Lib\\site-packages\\xlrd\\timemachine.py',
   'PYMODULE'),
  ('xlrd.xldate',
   'D:\\Anaconda\\Lib\\site-packages\\xlrd\\xldate.py',
   'PYMODULE'),
  ('xml', 'D:\\Anaconda\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.dom', 'D:\\Anaconda\\Lib\\xml\\dom\\__init__.py', 'PYMODULE'),
  ('xml.dom.NodeFilter',
   'D:\\Anaconda\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg', 'D:\\Anaconda\\Lib\\xml\\dom\\domreg.py', 'PYMODULE'),
  ('xml.dom.expatbuilder',
   'D:\\Anaconda\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'D:\\Anaconda\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom', 'D:\\Anaconda\\Lib\\xml\\dom\\minidom.py', 'PYMODULE'),
  ('xml.dom.pulldom', 'D:\\Anaconda\\Lib\\xml\\dom\\pulldom.py', 'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'D:\\Anaconda\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree', 'D:\\Anaconda\\Lib\\xml\\etree\\__init__.py', 'PYMODULE'),
  ('xml.etree.ElementInclude',
   'D:\\Anaconda\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'D:\\Anaconda\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'D:\\Anaconda\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'D:\\Anaconda\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers', 'D:\\Anaconda\\Lib\\xml\\parsers\\__init__.py', 'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\Anaconda\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax', 'D:\\Anaconda\\Lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\Anaconda\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\Anaconda\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler', 'D:\\Anaconda\\Lib\\xml\\sax\\handler.py', 'PYMODULE'),
  ('xml.sax.saxutils', 'D:\\Anaconda\\Lib\\xml\\sax\\saxutils.py', 'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\Anaconda\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc', 'D:\\Anaconda\\Lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xmlrpc.client', 'D:\\Anaconda\\Lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('xmlrpc.server', 'D:\\Anaconda\\Lib\\xmlrpc\\server.py', 'PYMODULE'),
  ('yaml', 'D:\\Anaconda\\Lib\\site-packages\\yaml\\__init__.py', 'PYMODULE'),
  ('yaml.composer',
   'D:\\Anaconda\\Lib\\site-packages\\yaml\\composer.py',
   'PYMODULE'),
  ('yaml.constructor',
   'D:\\Anaconda\\Lib\\site-packages\\yaml\\constructor.py',
   'PYMODULE'),
  ('yaml.cyaml',
   'D:\\Anaconda\\Lib\\site-packages\\yaml\\cyaml.py',
   'PYMODULE'),
  ('yaml.dumper',
   'D:\\Anaconda\\Lib\\site-packages\\yaml\\dumper.py',
   'PYMODULE'),
  ('yaml.emitter',
   'D:\\Anaconda\\Lib\\site-packages\\yaml\\emitter.py',
   'PYMODULE'),
  ('yaml.error',
   'D:\\Anaconda\\Lib\\site-packages\\yaml\\error.py',
   'PYMODULE'),
  ('yaml.events',
   'D:\\Anaconda\\Lib\\site-packages\\yaml\\events.py',
   'PYMODULE'),
  ('yaml.loader',
   'D:\\Anaconda\\Lib\\site-packages\\yaml\\loader.py',
   'PYMODULE'),
  ('yaml.nodes',
   'D:\\Anaconda\\Lib\\site-packages\\yaml\\nodes.py',
   'PYMODULE'),
  ('yaml.parser',
   'D:\\Anaconda\\Lib\\site-packages\\yaml\\parser.py',
   'PYMODULE'),
  ('yaml.reader',
   'D:\\Anaconda\\Lib\\site-packages\\yaml\\reader.py',
   'PYMODULE'),
  ('yaml.representer',
   'D:\\Anaconda\\Lib\\site-packages\\yaml\\representer.py',
   'PYMODULE'),
  ('yaml.resolver',
   'D:\\Anaconda\\Lib\\site-packages\\yaml\\resolver.py',
   'PYMODULE'),
  ('yaml.scanner',
   'D:\\Anaconda\\Lib\\site-packages\\yaml\\scanner.py',
   'PYMODULE'),
  ('yaml.serializer',
   'D:\\Anaconda\\Lib\\site-packages\\yaml\\serializer.py',
   'PYMODULE'),
  ('yaml.tokens',
   'D:\\Anaconda\\Lib\\site-packages\\yaml\\tokens.py',
   'PYMODULE'),
  ('yarl', 'D:\\Anaconda\\Lib\\site-packages\\yarl\\__init__.py', 'PYMODULE'),
  ('yarl._quoting',
   'D:\\Anaconda\\Lib\\site-packages\\yarl\\_quoting.py',
   'PYMODULE'),
  ('yarl._quoting_py',
   'D:\\Anaconda\\Lib\\site-packages\\yarl\\_quoting_py.py',
   'PYMODULE'),
  ('yarl._url', 'D:\\Anaconda\\Lib\\site-packages\\yarl\\_url.py', 'PYMODULE'),
  ('zipfile', 'D:\\Anaconda\\Lib\\zipfile.py', 'PYMODULE'),
  ('zipimport', 'D:\\Anaconda\\Lib\\zipimport.py', 'PYMODULE'),
  ('zipp', 'D:\\Anaconda\\Lib\\site-packages\\zipp\\__init__.py', 'PYMODULE'),
  ('zipp.py310compat',
   'D:\\Anaconda\\Lib\\site-packages\\zipp\\py310compat.py',
   'PYMODULE'),
  ('zope', 'D:\\Anaconda\\Lib\\site-packages\\zope\\__init__.py', 'PYMODULE'),
  ('zope.interface',
   'D:\\Anaconda\\Lib\\site-packages\\zope\\interface\\__init__.py',
   'PYMODULE'),
  ('zope.interface._compat',
   'D:\\Anaconda\\Lib\\site-packages\\zope\\interface\\_compat.py',
   'PYMODULE'),
  ('zope.interface.adapter',
   'D:\\Anaconda\\Lib\\site-packages\\zope\\interface\\adapter.py',
   'PYMODULE'),
  ('zope.interface.advice',
   'D:\\Anaconda\\Lib\\site-packages\\zope\\interface\\advice.py',
   'PYMODULE'),
  ('zope.interface.declarations',
   'D:\\Anaconda\\Lib\\site-packages\\zope\\interface\\declarations.py',
   'PYMODULE'),
  ('zope.interface.exceptions',
   'D:\\Anaconda\\Lib\\site-packages\\zope\\interface\\exceptions.py',
   'PYMODULE'),
  ('zope.interface.interface',
   'D:\\Anaconda\\Lib\\site-packages\\zope\\interface\\interface.py',
   'PYMODULE'),
  ('zope.interface.interfaces',
   'D:\\Anaconda\\Lib\\site-packages\\zope\\interface\\interfaces.py',
   'PYMODULE'),
  ('zope.interface.ro',
   'D:\\Anaconda\\Lib\\site-packages\\zope\\interface\\ro.py',
   'PYMODULE'),
  ('zstandard',
   'D:\\Anaconda\\Lib\\site-packages\\zstandard\\__init__.py',
   'PYMODULE'),
  ('zstandard.backend_cffi',
   'D:\\Anaconda\\Lib\\site-packages\\zstandard\\backend_cffi.py',
   'PYMODULE')])
