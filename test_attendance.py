#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本：验证签到检查系统的核心功能
"""

import pandas as pd
import os
from datetime import datetime

def create_test_data():
    """创建测试数据"""
    print("创建测试数据...")
    
    # 创建全院学生名单测试数据
    all_students_data = {
        '序号': range(1, 21),
        '教学单位': ['艺术学院'] * 20,
        '姓名': [f'学生{i:02d}' for i in range(1, 21)],
        '学号': [f'2024{i:04d}' for i in range(1, 21)],
        '年级': ['2024级'] * 20,
        '专业班级': ['美术1班'] * 10 + ['音乐1班'] * 10,
        '班主任姓名': ['张老师'] * 10 + ['李老师'] * 10,
        '备注': ['本科'] * 20
    }
    
    all_students_df = pd.DataFrame(all_students_data)
    all_students_df.to_excel('测试_全院学生名单.xlsx', index=False)
    
    # 创建签到名单测试数据（假设有15个学生签到了）
    attended_students = [f'学生{i:02d}' for i in [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]]
    attendance_data = {
        '姓名': attended_students,
        '签到时间': ['09:00'] * len(attended_students)
    }
    
    attendance_df = pd.DataFrame(attendance_data)
    attendance_df.to_excel('测试_签到名单.xlsx', index=False)
    
    print("测试数据创建完成！")
    print("- 测试_全院学生名单.xlsx (20个学生)")
    print("- 测试_签到名单.xlsx (15个学生签到)")
    print("预期结果：5个学生未签到")

def test_attendance_logic():
    """测试签到逻辑"""
    print("\n开始测试签到逻辑...")
    
    try:
        # 读取测试数据
        all_students_df = pd.read_excel('测试_全院学生名单.xlsx')
        attendance_df = pd.read_excel('测试_签到名单.xlsx')
        
        print(f"全院学生数：{len(all_students_df)}")
        print(f"签到学生数：{len(attendance_df)}")
        
        # 执行签到检查逻辑
        attended_names = set(attendance_df['姓名'].dropna().astype(str))
        absent_students = all_students_df[~all_students_df['姓名'].astype(str).isin(attended_names)]
        
        print(f"未签到学生数：{len(absent_students)}")
        print("未签到学生名单：")
        for _, student in absent_students.iterrows():
            print(f"  - {student['姓名']} ({student['学号']}) - {student['专业班级']}")
        
        # 统计各班级未签到人数
        class_stats = absent_students.groupby('专业班级').size().to_dict()
        print("\n各班级未签到统计：")
        for class_name, count in class_stats.items():
            print(f"  - {class_name}：{count}人")
        
        # 保存未签到学生名单
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"测试_未签到学生名单_{timestamp}.xlsx"
        absent_students.to_excel(output_file, index=False)
        print(f"\n未签到学生名单已保存：{output_file}")
        
        print("\n✅ 测试通过！逻辑正确。")
        
    except Exception as e:
        print(f"❌ 测试失败：{str(e)}")

def check_dependencies():
    """检查依赖包"""
    print("检查依赖包...")
    
    required_packages = ['pandas', 'PyQt5', 'openpyxl', 'xlrd']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} - 已安装")
        except ImportError:
            print(f"❌ {package} - 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n需要安装的包：{', '.join(missing_packages)}")
        print("请运行：pip install " + " ".join(missing_packages))
        return False
    else:
        print("\n✅ 所有依赖包都已安装！")
        return True

def main():
    """主测试函数"""
    print("=" * 50)
    print("艺术学院签到检查系统 - 测试程序")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        return
    
    print("\n" + "=" * 50)
    
    # 创建测试数据
    create_test_data()
    
    print("\n" + "=" * 50)
    
    # 测试逻辑
    test_attendance_logic()
    
    print("\n" + "=" * 50)
    print("测试完成！如果所有测试都通过，说明程序可以正常工作。")
    print("现在可以运行主程序：python attendance_checker.py")

if __name__ == "__main__":
    main()
